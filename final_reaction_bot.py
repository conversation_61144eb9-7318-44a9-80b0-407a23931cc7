#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🤖 بوت مراقبة التفاعلات النهائي
مع أسماء المستخدمين المتفاعلين
"""

import asyncio
import os
import sqlite3
from datetime import datetime
from dotenv import load_dotenv
from telethon import TelegramClient, events, Button
from telethon.tl.types import MessageReactions, ReactionCount

# تحميل متغيرات البيئة
load_dotenv()

class FinalReactionBot:
    def __init__(self):
        self.api_id = int(os.getenv('API_ID'))
        self.api_hash = os.getenv('API_HASH')
        self.bot_token = os.getenv('BOT_TOKEN')
        self.phone_number = os.getenv('PHONE_NUMBER')
        self.admin_user_id = int(os.getenv('ADMIN_USER_ID'))
        
        # إعداد البوت
        self.bot = TelegramClient('final_reaction_bot', self.api_id, self.api_hash)
        
        # إعداد قاعدة البيانات
        self.init_database()
        
        # متغيرات المراقبة
        self.monitoring_channel = None
        self.is_monitoring = False
        self.monitor_client = None
        self.scan_task = None
        self.last_reactions = {}  # لتتبع التفاعلات السابقة
    
    def init_database(self):
        """إنشاء قاعدة بيانات محسنة"""
        try:
            conn = sqlite3.connect('final_reactions.db')
            cursor = conn.cursor()
            
            # جدول الرسائل
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS messages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_name TEXT,
                    message_id INTEGER,
                    message_text TEXT,
                    message_date DATETIME,
                    total_reactions INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(channel_name, message_id)
                )
            ''')
            
            # جدول التفاعلات مع أسماء المستخدمين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS user_reactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_name TEXT,
                    message_id INTEGER,
                    user_id INTEGER,
                    user_name TEXT,
                    user_username TEXT,
                    reaction_emoji TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(channel_name, message_id, user_id, reaction_emoji)
                )
            ''')
            
            # جدول إحصائيات التفاعلات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS reaction_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_name TEXT,
                    message_id INTEGER,
                    reaction_emoji TEXT,
                    total_count INTEGER,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(channel_name, message_id, reaction_emoji)
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة البيانات النهائية")
            
        except Exception as e:
            print(f"❌ خطأ في قاعدة البيانات: {e}")
    
    async def start(self):
        """بدء تشغيل البوت"""
        try:
            await self.bot.start(bot_token=self.bot_token)
            print("✅ تم تشغيل البوت بنجاح!")
            
            # تسجيل معالجات الأوامر
            self.register_handlers()
            
            # إرسال رسالة ترحيب
            if self.admin_user_id:
                await self.send_welcome_message()
            
            print("🤖 البوت جاهز للاستخدام!")
            
        except Exception as e:
            print(f"❌ خطأ في تشغيل البوت: {e}")
    
    def register_handlers(self):
        """تسجيل معالجات الأوامر"""
        
        @self.bot.on(events.NewMessage(pattern='/start'))
        async def start_command(event):
            await self.handle_start(event)
        
        @self.bot.on(events.NewMessage(pattern='/help'))
        async def help_command(event):
            await self.handle_help(event)
        
        @self.bot.on(events.NewMessage(pattern='/monitor'))
        async def monitor_command(event):
            await self.handle_monitor(event)
        
        @self.bot.on(events.NewMessage(pattern='/stop'))
        async def stop_command(event):
            await self.handle_stop_monitoring(event)
        
        @self.bot.on(events.NewMessage(pattern='/stats'))
        async def stats_command(event):
            await self.handle_stats(event)
        
        @self.bot.on(events.NewMessage(pattern='/scan'))
        async def scan_command(event):
            await self.handle_manual_scan(event)
        
        @self.bot.on(events.NewMessage(pattern='/users'))
        async def users_command(event):
            await self.handle_top_users(event)
        
        @self.bot.on(events.CallbackQuery)
        async def callback_handler(event):
            await self.handle_callback(event)
    
    async def handle_start(self, event):
        """معالجة أمر البدء"""
        if not self.is_admin(event.sender_id):
            await event.respond("❌ عذراً، هذا البوت مخصص للمدير فقط.")
            return
        
        welcome_text = """
🤖 **مرحباً بك في بوت مراقبة التفاعلات النهائي!**

**المميزات الجديدة:**
👥 عرض أسماء المستخدمين المتفاعلين
🔍 مسح دوري للتفاعلات كل 30 ثانية
📊 إحصائيات مفصلة مع أسماء المستخدمين
💾 حفظ جميع البيانات

**الأوامر المتاحة:**
/help - عرض المساعدة
/monitor @channel - بدء مراقبة قناة
/stop - إيقاف المراقبة
/stats - عرض الإحصائيات
/scan - مسح يدوي للتفاعلات
/users - أكثر المستخدمين تفاعلاً
        """
        
        buttons = [
            [Button.inline("📊 الإحصائيات", b"stats")],
            [Button.inline("👥 أكثر المتفاعلين", b"users")],
            [Button.inline("🔍 مسح التفاعلات", b"scan")],
            [Button.inline("❓ المساعدة", b"help")]
        ]
        
        await event.respond(welcome_text, buttons=buttons)
    
    async def handle_help(self, event):
        """معالجة أمر المساعدة"""
        if not self.is_admin(event.sender_id):
            return
        
        help_text = """
📖 **دليل الاستخدام المفصل:**

**🔍 المراقبة:**
• `/monitor @channel_name` - بدء مراقبة قناة
• `/stop` - إيقاف المراقبة
• `/scan` - مسح يدوي فوري للتفاعلات

**📊 الإحصائيات:**
• `/stats` - إحصائيات شاملة
• `/users` - أكثر المستخدمين تفاعلاً

**💡 كيف يعمل:**
1. البوت يمسح القناة كل 30 ثانية
2. يحصل على أسماء المستخدمين المتفاعلين
3. يرسل تنبيهات مفصلة مع أسماء المتفاعلين
4. يحفظ جميع البيانات في قاعدة بيانات

**🔔 مثال على التنبيه:**
"🔔 تفاعل جديد!
👤 المستخدم: أحمد محمد (@ahmed123)
❤️ التفاعل: ❤️
📺 القناة: اسم القناة
💬 الرسالة: 123"

**⚠️ ملاحظات:**
• تأكد من أن البوت عضو في القناة
• القناة يجب أن تسمح بالتفاعلات
• بعض المستخدمين قد يخفون هويتهم
        """
        
        await event.respond(help_text)
    
    async def handle_monitor(self, event):
        """معالجة أمر بدء المراقبة"""
        if not self.is_admin(event.sender_id):
            return
        
        parts = event.message.message.split()
        if len(parts) < 2:
            await event.respond("❌ يرجى تحديد القناة: `/monitor @channel_name`")
            return
        
        channel_name = parts[1]
        
        if self.is_monitoring:
            await event.respond("⚠️ المراقبة قيد التشغيل بالفعل. استخدم /stop لإيقافها أولاً.")
            return
        
        try:
            await event.respond("🔄 جاري بدء المراقبة...")
            
            # إنشاء عميل المراقبة
            self.monitor_client = TelegramClient('monitor_session', self.api_id, self.api_hash)
            await self.monitor_client.start(phone=self.phone_number)
            
            # الحصول على القناة
            entity = await self.monitor_client.get_entity(channel_name)
            self.monitoring_channel = entity
            
            # بدء المسح الدوري
            await self.start_periodic_scan()
            
            self.is_monitoring = True
            
            await event.respond(f"✅ تم بدء مراقبة القناة: **{entity.title}**\n\n🔍 البوت يمسح التفاعلات كل 30 ثانية\n👥 يعرض أسماء المستخدمين المتفاعلين\n📊 استخدم /stats لرؤية الإحصائيات\n👥 استخدم /users لرؤية أكثر المتفاعلين")
            
        except Exception as e:
            await event.respond(f"❌ فشل في بدء المراقبة: {str(e)}")
    
    async def start_periodic_scan(self):
        """بدء المسح الدوري للتفاعلات"""
        async def scan_loop():
            while self.is_monitoring:
                try:
                    await self.scan_reactions_with_users()
                    await asyncio.sleep(30)  # مسح كل 30 ثانية
                except Exception as e:
                    print(f"خطأ في المسح الدوري: {e}")
                    await asyncio.sleep(60)  # انتظار أطول في حالة الخطأ
        
        self.scan_task = asyncio.create_task(scan_loop())
        print("✅ بدأ المسح الدوري للتفاعلات مع أسماء المستخدمين")

    async def scan_reactions_with_users(self):
        """مسح التفاعلات مع الحصول على أسماء المستخدمين"""
        try:
            if not self.monitoring_channel or not self.monitor_client:
                return

            # الحصول على آخر 20 رسالة
            messages = []
            async for message in self.monitor_client.iter_messages(self.monitoring_channel, limit=20):
                messages.append(message)

            new_reactions_found = False

            for message in messages:
                if message.reactions:
                    # حفظ الرسالة
                    await self.save_message(message)

                    # فحص كل تفاعل
                    for reaction in message.reactions.results:
                        if hasattr(reaction, 'reaction') and hasattr(reaction.reaction, 'emoticon'):
                            emoji = reaction.reaction.emoticon
                            count = reaction.count

                            # مقارنة مع التفاعلات السابقة
                            message_key = f"{self.monitoring_channel.id}_{message.id}_{emoji}"
                            old_count = self.last_reactions.get(message_key, 0)

                            if count > old_count:
                                # تفاعل جديد - الحصول على أسماء المستخدمين
                                users = await self.get_reaction_users(message, reaction.reaction)

                                if users:
                                    for user in users:
                                        # التحقق من أن هذا المستخدم جديد
                                        if not await self.user_reaction_exists(message.id, user.id, emoji):
                                            await self.notify_new_user_reaction(message, emoji, user)
                                            await self.save_user_reaction(message, emoji, user)
                                            new_reactions_found = True

                                # تحديث العدد المحفوظ
                                self.last_reactions[message_key] = count
                                await self.save_reaction_stats(message, emoji, count)

            if new_reactions_found:
                print(f"🔍 تم العثور على تفاعلات جديدة - {datetime.now().strftime('%H:%M:%S')}")

        except Exception as e:
            print(f"خطأ في مسح التفاعلات: {e}")

    async def get_reaction_users(self, message, reaction):
        """الحصول على قائمة المستخدمين الذين تفاعلوا"""
        try:
            # محاولة الحصول على قائمة المتفاعلين
            participants = await self.monitor_client.get_message_reactions(
                entity=self.monitoring_channel,
                msg_id=message.id,
                reaction=reaction
            )

            if participants and hasattr(participants, 'users'):
                return participants.users

            return []

        except Exception as e:
            print(f"لا يمكن الحصول على قائمة المتفاعلين: {e}")
            return []

    async def user_reaction_exists(self, message_id, user_id, emoji):
        """التحقق من وجود تفاعل المستخدم مسبقاً"""
        try:
            conn = sqlite3.connect('final_reactions.db')
            cursor = conn.cursor()

            cursor.execute('''
                SELECT COUNT(*) FROM user_reactions
                WHERE message_id = ? AND user_id = ? AND reaction_emoji = ?
            ''', (message_id, user_id, emoji))

            count = cursor.fetchone()[0]
            conn.close()

            return count > 0

        except Exception as e:
            print(f"خطأ في التحقق من وجود التفاعل: {e}")
            return False

    async def notify_new_user_reaction(self, message, emoji, user):
        """إرسال تنبيه بتفاعل مستخدم جديد"""
        try:
            user_name = f"{user.first_name or ''} {user.last_name or ''}".strip()
            user_username = f"@{user.username}" if user.username else "بدون معرف"
            message_text = message.message[:50] + "..." if len(message.message) > 50 else message.message

            notification = f"""
🔔 **تفاعل جديد!**

👤 **المستخدم:** {user_name}
🆔 **المعرف:** {user_username}
{emoji} **التفاعل:** {emoji}

📺 **القناة:** {self.monitoring_channel.title}
💬 **الرسالة:** {message.id}
📝 **النص:** {message_text}

⏰ **الوقت:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """

            await self.bot.send_message(self.admin_user_id, notification)

        except Exception as e:
            print(f"خطأ في إرسال التنبيه: {e}")

    async def save_message(self, message):
        """حفظ رسالة في قاعدة البيانات"""
        try:
            conn = sqlite3.connect('final_reactions.db')
            cursor = conn.cursor()

            total_reactions = sum(r.count for r in message.reactions.results) if message.reactions else 0

            cursor.execute('''
                INSERT OR REPLACE INTO messages
                (channel_name, message_id, message_text, message_date, total_reactions)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                self.monitoring_channel.title,
                message.id,
                message.message or "",
                message.date,
                total_reactions
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"خطأ في حفظ الرسالة: {e}")

    async def save_user_reaction(self, message, emoji, user):
        """حفظ تفاعل المستخدم في قاعدة البيانات"""
        try:
            conn = sqlite3.connect('final_reactions.db')
            cursor = conn.cursor()

            user_name = f"{user.first_name or ''} {user.last_name or ''}".strip()
            user_username = user.username or ""

            cursor.execute('''
                INSERT OR REPLACE INTO user_reactions
                (channel_name, message_id, user_id, user_name, user_username, reaction_emoji)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                self.monitoring_channel.title,
                message.id,
                user.id,
                user_name,
                user_username,
                emoji
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"خطأ في حفظ تفاعل المستخدم: {e}")

    async def save_reaction_stats(self, message, emoji, count):
        """حفظ إحصائيات التفاعل"""
        try:
            conn = sqlite3.connect('final_reactions.db')
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO reaction_stats
                (channel_name, message_id, reaction_emoji, total_count)
                VALUES (?, ?, ?, ?)
            ''', (self.monitoring_channel.title, message.id, emoji, count))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"خطأ في حفظ إحصائيات التفاعل: {e}")

    async def handle_manual_scan(self, event):
        """معالجة أمر المسح اليدوي"""
        if not self.is_admin(event.sender_id):
            return

        if not self.is_monitoring:
            await event.respond("⚠️ المراقبة غير مفعلة. استخدم /monitor أولاً.")
            return

        await event.respond("🔍 جاري المسح اليدوي...")
        await self.scan_reactions_with_users()
        await event.respond("✅ تم المسح اليدوي بنجاح!")

    async def handle_stop_monitoring(self, event):
        """معالجة أمر إيقاف المراقبة"""
        if not self.is_admin(event.sender_id):
            return

        if not self.is_monitoring:
            await event.respond("⚠️ المراقبة غير مفعلة حالياً.")
            return

        # إيقاف المسح الدوري
        if self.scan_task:
            self.scan_task.cancel()
            self.scan_task = None

        # إيقاف عميل المراقبة
        if self.monitor_client:
            await self.monitor_client.disconnect()
            self.monitor_client = None

        self.is_monitoring = False
        self.monitoring_channel = None
        self.last_reactions = {}

        await event.respond("✅ تم إيقاف المراقبة بنجاح.")

    async def handle_stats(self, event):
        """معالجة أمر الإحصائيات"""
        if not self.is_admin(event.sender_id):
            return

        try:
            conn = sqlite3.connect('final_reactions.db')
            cursor = conn.cursor()

            # إحصائيات عامة
            cursor.execute("SELECT COUNT(*) FROM user_reactions")
            total_user_reactions = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM messages")
            total_messages = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(DISTINCT user_id) FROM user_reactions")
            unique_users = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(DISTINCT reaction_emoji) FROM user_reactions")
            unique_emojis = cursor.fetchone()[0]

            # أكثر الإيموجي استخداماً
            cursor.execute("""
                SELECT reaction_emoji, COUNT(*) as total
                FROM user_reactions
                GROUP BY reaction_emoji
                ORDER BY total DESC
                LIMIT 5
            """)
            top_emojis = cursor.fetchall()

            # آخر التفاعلات
            cursor.execute("""
                SELECT user_name, user_username, reaction_emoji, timestamp
                FROM user_reactions
                ORDER BY timestamp DESC
                LIMIT 5
            """)
            recent_reactions = cursor.fetchall()

            conn.close()

            # تنسيق الإحصائيات
            stats_text = f"""
📊 **إحصائيات البوت المفصلة:**

🔄 **حالة المراقبة:** {"🟢 نشط" if self.is_monitoring else "🔴 متوقف"}
📺 **القناة المراقبة:** {getattr(self.monitoring_channel, 'title', 'لا توجد') if self.monitoring_channel else 'لا توجد'}

📈 **الإحصائيات العامة:**
💬 إجمالي الرسائل: {total_messages}
🎭 إجمالي التفاعلات: {total_user_reactions}
👥 المستخدمين المتفاعلين: {unique_users}
😀 أنواع الإيموجي: {unique_emojis}

🏆 **أكثر الإيموجي استخداماً:**
"""

            for i, (emoji, count) in enumerate(top_emojis, 1):
                stats_text += f"{i}. {emoji} - {count} مرة\n"

            if recent_reactions:
                stats_text += "\n🕐 **آخر التفاعلات:**\n"
                for reaction in recent_reactions:
                    user_name, user_username, emoji, timestamp = reaction
                    username_text = f"@{user_username}" if user_username else ""
                    stats_text += f"• {user_name} {username_text} - {emoji}\n"

            stats_text += f"\n📅 **آخر تحديث:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

            await event.respond(stats_text)

        except Exception as e:
            await event.respond(f"❌ خطأ في الحصول على الإحصائيات: {str(e)}")

    async def handle_top_users(self, event):
        """معالجة أمر أكثر المستخدمين تفاعلاً"""
        if not self.is_admin(event.sender_id):
            return

        try:
            conn = sqlite3.connect('final_reactions.db')
            cursor = conn.cursor()

            cursor.execute("""
                SELECT user_name, user_username, COUNT(*) as reaction_count
                FROM user_reactions
                GROUP BY user_id, user_name, user_username
                ORDER BY reaction_count DESC
                LIMIT 10
            """)
            top_users = cursor.fetchall()

            conn.close()

            if not top_users:
                await event.respond("📭 لا توجد بيانات مستخدمين حتى الآن.")
                return

            users_text = "👥 **أكثر 10 مستخدمين تفاعلاً:**\n\n"

            for i, (user_name, user_username, count) in enumerate(top_users, 1):
                username_text = f"@{user_username}" if user_username else "بدون معرف"
                users_text += f"{i}. **{user_name}** {username_text}\n   💬 {count} تفاعل\n\n"

            await event.respond(users_text)

        except Exception as e:
            await event.respond(f"❌ خطأ في الحصول على أكثر المستخدمين تفاعلاً: {str(e)}")

    async def handle_callback(self, event):
        """معالجة أزرار الاستجابة"""
        if not self.is_admin(event.sender_id):
            return

        data = event.data.decode('utf-8')

        if data == "stats":
            await self.handle_stats(event)
        elif data == "users":
            await self.handle_top_users(event)
        elif data == "scan":
            await self.handle_manual_scan(event)
        elif data == "help":
            await self.handle_help(event)

        await event.answer()

    def is_admin(self, user_id: int) -> bool:
        """التحقق من صلاحيات المدير"""
        return user_id == self.admin_user_id

    async def send_welcome_message(self):
        """إرسال رسالة ترحيب للمدير"""
        try:
            message = "🤖 تم تشغيل بوت مراقبة التفاعلات النهائي بنجاح!\n\n👥 يعرض أسماء المستخدمين المتفاعلين\n🔍 مسح دوري كل 30 ثانية\n📊 إحصائيات مفصلة\n💾 حفظ جميع البيانات\n\nاستخدم /start للبدء."
            await self.bot.send_message(self.admin_user_id, message)
        except Exception as e:
            print(f"لا يمكن إرسال رسالة الترحيب: {e}")

    async def run(self):
        """تشغيل البوت"""
        await self.start()
        await self.bot.run_until_disconnected()

async def main():
    print("🔄 بدء تشغيل بوت مراقبة التفاعلات النهائي...")
    bot = FinalReactionBot()
    await bot.run()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
