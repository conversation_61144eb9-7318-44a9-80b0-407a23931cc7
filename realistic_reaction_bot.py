#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🤖 بوت مراقبة التفاعلات الواقعي
يركز على ما يمكن تحقيقه فعلياً مع قيود Telegram
"""

import asyncio
import os
import sqlite3
from datetime import datetime
from dotenv import load_dotenv
from telethon import TelegramClient, events, Button

# تحميل متغيرات البيئة
load_dotenv()

class RealisticReactionBot:
    def __init__(self):
        self.api_id = int(os.getenv('API_ID'))
        self.api_hash = os.getenv('API_HASH')
        self.bot_token = os.getenv('BOT_TOKEN')
        self.phone_number = os.getenv('PHONE_NUMBER')
        self.admin_user_id = int(os.getenv('ADMIN_USER_ID'))
        
        # إعداد البوت
        self.bot = TelegramClient('realistic_reaction_bot', self.api_id, self.api_hash)
        
        # إعداد قاعدة البيانات
        self.init_database()
        
        # متغيرات المراقبة
        self.monitoring_channel = None
        self.is_monitoring = False
        self.monitor_client = None
        self.scan_task = None
        self.last_reactions = {}
    
    def init_database(self):
        """إنشاء قاعدة بيانات بسيطة"""
        try:
            conn = sqlite3.connect('realistic_reactions.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS reactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_name TEXT,
                    message_id INTEGER,
                    message_text TEXT,
                    reaction_emoji TEXT,
                    reaction_count INTEGER,
                    detected_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS channel_activity (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_name TEXT,
                    active_users_count INTEGER,
                    last_scan DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة البيانات الواقعية")
            
        except Exception as e:
            print(f"❌ خطأ في قاعدة البيانات: {e}")
    
    async def start(self):
        """بدء تشغيل البوت"""
        try:
            await self.bot.start(bot_token=self.bot_token)
            print("✅ تم تشغيل البوت بنجاح!")
            
            self.register_handlers()
            
            if self.admin_user_id:
                await self.send_welcome_message()
            
            print("🤖 البوت جاهز للاستخدام!")
            
        except Exception as e:
            print(f"❌ خطأ في تشغيل البوت: {e}")
    
    def register_handlers(self):
        """تسجيل معالجات الأوامر"""
        
        @self.bot.on(events.NewMessage(pattern='/start'))
        async def start_command(event):
            await self.handle_start(event)
        
        @self.bot.on(events.NewMessage(pattern='/monitor'))
        async def monitor_command(event):
            await self.handle_monitor(event)
        
        @self.bot.on(events.NewMessage(pattern='/stop'))
        async def stop_command(event):
            await self.handle_stop_monitoring(event)
        
        @self.bot.on(events.NewMessage(pattern='/stats'))
        async def stats_command(event):
            await self.handle_stats(event)
        
        @self.bot.on(events.NewMessage(pattern='/info'))
        async def info_command(event):
            await self.handle_message_info(event)
        
        @self.bot.on(events.NewMessage(pattern='/activity'))
        async def activity_command(event):
            await self.handle_channel_activity(event)
    
    async def handle_start(self, event):
        """معالجة أمر البدء"""
        if not self.is_admin(event.sender_id):
            await event.respond("❌ عذراً، هذا البوت مخصص للمدير فقط.")
            return
        
        welcome_text = """
🤖 **بوت مراقبة التفاعلات الواقعي**

**ما يمكن للبوت فعله:**
🔍 مراقبة التفاعلات على الرسائل
📊 إحصائيات التفاعلات والإيموجي
📈 تتبع نشاط القناة
💾 حفظ جميع البيانات

**ما لا يمكن فعله (قيود Telegram):**
❌ عرض أسماء المتفاعلين في القنوات العامة
❌ الحصول على قائمة مفصلة للمستخدمين المتفاعلين
❌ تتبع المتفاعلين بشكل فردي

**الأوامر:**
/monitor @channel - بدء المراقبة
/stats - إحصائيات التفاعلات
/info 123 - معلومات عن رسالة معينة
/activity - نشاط القناة
/stop - إيقاف المراقبة
        """
        
        await event.respond(welcome_text)
    
    async def handle_monitor(self, event):
        """معالجة أمر بدء المراقبة"""
        if not self.is_admin(event.sender_id):
            return
        
        parts = event.message.message.split()
        if len(parts) < 2:
            await event.respond("❌ يرجى تحديد القناة: `/monitor @channel_name`")
            return
        
        channel_name = parts[1]
        
        if self.is_monitoring:
            await event.respond("⚠️ المراقبة قيد التشغيل بالفعل. استخدم /stop لإيقافها أولاً.")
            return
        
        try:
            await event.respond("🔄 جاري بدء المراقبة...")
            
            self.monitor_client = TelegramClient('monitor_session', self.api_id, self.api_hash)
            await self.monitor_client.start(phone=self.phone_number)
            
            entity = await self.monitor_client.get_entity(channel_name)
            self.monitoring_channel = entity
            
            await self.start_periodic_scan()
            self.is_monitoring = True
            
            await event.respond(f"✅ تم بدء مراقبة القناة: **{entity.title}**\n\n🔍 البوت يراقب التفاعلات كل 30 ثانية\n📊 استخدم /stats للإحصائيات\n📈 استخدم /activity لنشاط القناة")
            
        except Exception as e:
            await event.respond(f"❌ فشل في بدء المراقبة: {str(e)}")
    
    async def start_periodic_scan(self):
        """بدء المسح الدوري"""
        async def scan_loop():
            while self.is_monitoring:
                try:
                    await self.scan_reactions()
                    await asyncio.sleep(30)
                except Exception as e:
                    print(f"خطأ في المسح الدوري: {e}")
                    await asyncio.sleep(60)
        
        self.scan_task = asyncio.create_task(scan_loop())
        print("✅ بدأ المسح الدوري للتفاعلات")
    
    async def scan_reactions(self):
        """مسح التفاعلات"""
        try:
            if not self.monitoring_channel or not self.monitor_client:
                return
            
            messages = []
            async for message in self.monitor_client.iter_messages(self.monitoring_channel, limit=20):
                messages.append(message)
            
            new_reactions_found = False
            
            for message in messages:
                if message.reactions:
                    for reaction in message.reactions.results:
                        if hasattr(reaction, 'reaction') and hasattr(reaction.reaction, 'emoticon'):
                            emoji = reaction.reaction.emoticon
                            count = reaction.count
                            
                            message_key = f"{self.monitoring_channel.id}_{message.id}_{emoji}"
                            old_count = self.last_reactions.get(message_key, 0)
                            
                            if count > old_count:
                                await self.notify_new_reaction(message, emoji, count, count - old_count)
                                await self.save_reaction(message, emoji, count)
                                new_reactions_found = True
                                self.last_reactions[message_key] = count
            
            if new_reactions_found:
                print(f"🔍 تم العثور على تفاعلات جديدة - {datetime.now().strftime('%H:%M:%S')}")
            
        except Exception as e:
            print(f"خطأ في مسح التفاعلات: {e}")
    
    async def notify_new_reaction(self, message, emoji, total_count, new_count):
        """إرسال تنبيه بتفاعل جديد"""
        try:
            message_text = message.message[:50] + "..." if len(message.message) > 50 else message.message
            
            notification = f"""
🔔 **تفاعل جديد!**

{emoji} **التفاعل:** {emoji}
📊 **العدد الكلي:** {total_count}
🆕 **جديد:** +{new_count}

📺 **القناة:** {self.monitoring_channel.title}
💬 **الرسالة:** {message.id}
📝 **النص:** {message_text}

⏰ **الوقت:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

💡 **استخدم** `/info {message.id}` **لمزيد من التفاصيل**
            """
            
            await self.bot.send_message(self.admin_user_id, notification)
            
        except Exception as e:
            print(f"خطأ في إرسال التنبيه: {e}")
    
    async def save_reaction(self, message, emoji, count):
        """حفظ تفاعل في قاعدة البيانات"""
        try:
            conn = sqlite3.connect('realistic_reactions.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO reactions 
                (channel_name, message_id, message_text, reaction_emoji, reaction_count)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                self.monitoring_channel.title,
                message.id,
                message.message or "",
                emoji,
                count
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في حفظ التفاعل: {e}")
    
    async def handle_message_info(self, event):
        """معالجة أمر معلومات الرسالة"""
        if not self.is_admin(event.sender_id):
            return
        
        parts = event.message.message.split()
        if len(parts) < 2:
            await event.respond("❌ يرجى تحديد معرف الرسالة: `/info 123`")
            return
        
        try:
            message_id = int(parts[1])
            
            if not self.monitoring_channel or not self.monitor_client:
                await event.respond("⚠️ المراقبة غير مفعلة. استخدم /monitor أولاً.")
                return
            
            # البحث عن الرسالة
            message = await self.get_message_by_id(message_id)
            
            if not message:
                await event.respond(f"❌ لم يتم العثور على الرسالة رقم {message_id}")
                return
            
            # إنشاء معلومات مفصلة
            info_text = f"""
📋 **معلومات الرسالة {message_id}:**

📝 **النص:** {message.message[:200] + '...' if len(message.message) > 200 else message.message}
📅 **التاريخ:** {message.date.strftime('%Y-%m-%d %H:%M:%S')}
👁️ **المشاهدات:** {message.views or 'غير متاح'}
"""
            
            if message.reactions:
                info_text += "\n🎭 **التفاعلات:**\n"
                total_reactions = 0
                for reaction in message.reactions.results:
                    if hasattr(reaction, 'reaction') and hasattr(reaction.reaction, 'emoticon'):
                        emoji = reaction.reaction.emoticon
                        count = reaction.count
                        total_reactions += count
                        info_text += f"   {emoji} {count}\n"
                
                info_text += f"\n📊 **إجمالي التفاعلات:** {total_reactions}"
            else:
                info_text += "\n📭 **لا توجد تفاعلات**"
            
            # معلومات إضافية من قاعدة البيانات
            conn = sqlite3.connect('realistic_reactions.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                SELECT reaction_emoji, reaction_count, detected_at 
                FROM reactions 
                WHERE message_id = ? 
                ORDER BY detected_at DESC 
                LIMIT 5
            ''', (message_id,))
            
            db_reactions = cursor.fetchall()
            conn.close()
            
            if db_reactions:
                info_text += "\n\n📈 **تاريخ التفاعلات:**\n"
                for emoji, count, detected_at in db_reactions:
                    info_text += f"   {emoji} {count} - {detected_at}\n"
            
            await event.respond(info_text)
            
        except ValueError:
            await event.respond("❌ معرف الرسالة يجب أن يكون رقم صحيح.")
        except Exception as e:
            await event.respond(f"❌ خطأ: {str(e)}")
    
    async def get_message_by_id(self, message_id):
        """البحث عن رسالة بمعرفها"""
        try:
            async for message in self.monitor_client.iter_messages(self.monitoring_channel, limit=100):
                if message.id == message_id:
                    return message
            return None
        except Exception as e:
            print(f"خطأ في البحث عن الرسالة: {e}")
            return None

    async def handle_channel_activity(self, event):
        """معالجة أمر نشاط القناة"""
        if not self.is_admin(event.sender_id):
            return

        if not self.monitoring_channel or not self.monitor_client:
            await event.respond("⚠️ المراقبة غير مفعلة. استخدم /monitor أولاً.")
            return

        try:
            await event.respond("🔍 جاري تحليل نشاط القناة...")

            # تحليل آخر 50 رسالة
            messages_with_reactions = 0
            total_reactions = 0
            popular_emojis = {}
            recent_activity = []

            async for message in self.monitor_client.iter_messages(self.monitoring_channel, limit=50):
                if message.reactions:
                    messages_with_reactions += 1
                    for reaction in message.reactions.results:
                        if hasattr(reaction, 'reaction') and hasattr(reaction.reaction, 'emoticon'):
                            emoji = reaction.reaction.emoticon
                            count = reaction.count
                            total_reactions += count
                            popular_emojis[emoji] = popular_emojis.get(emoji, 0) + count

                            recent_activity.append({
                                'message_id': message.id,
                                'emoji': emoji,
                                'count': count,
                                'date': message.date
                            })

            # ترتيب الإيموجي حسب الشعبية
            sorted_emojis = sorted(popular_emojis.items(), key=lambda x: x[1], reverse=True)[:5]

            activity_text = f"""
📈 **تحليل نشاط القناة: {self.monitoring_channel.title}**

📊 **إحصائيات عامة:**
💬 الرسائل المفحوصة: 50
🎭 رسائل بها تفاعلات: {messages_with_reactions}
📈 إجمالي التفاعلات: {total_reactions}
📊 معدل التفاعل: {(messages_with_reactions/50)*100:.1f}%

🏆 **أكثر الإيموجي استخداماً:**
"""

            for i, (emoji, count) in enumerate(sorted_emojis, 1):
                activity_text += f"{i}. {emoji} - {count} مرة\n"

            if not sorted_emojis:
                activity_text += "لا توجد تفاعلات في آخر 50 رسالة\n"

            # آخر النشاطات
            recent_activity.sort(key=lambda x: x['date'], reverse=True)
            if recent_activity:
                activity_text += "\n🕐 **آخر التفاعلات:**\n"
                for activity in recent_activity[:5]:
                    activity_text += f"• {activity['emoji']} على الرسالة {activity['message_id']} ({activity['count']})\n"

            await event.respond(activity_text)

        except Exception as e:
            await event.respond(f"❌ خطأ في تحليل النشاط: {str(e)}")

    async def handle_stats(self, event):
        """معالجة أمر الإحصائيات"""
        if not self.is_admin(event.sender_id):
            return

        try:
            conn = sqlite3.connect('realistic_reactions.db')
            cursor = conn.cursor()

            # إحصائيات من قاعدة البيانات
            cursor.execute("SELECT COUNT(*) FROM reactions")
            total_reactions = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(DISTINCT message_id) FROM reactions")
            unique_messages = cursor.fetchone()[0]

            cursor.execute("""
                SELECT reaction_emoji, SUM(reaction_count) as total
                FROM reactions
                GROUP BY reaction_emoji
                ORDER BY total DESC
                LIMIT 5
            """)
            top_emojis = cursor.fetchall()

            cursor.execute("""
                SELECT message_id, reaction_emoji, reaction_count, detected_at
                FROM reactions
                ORDER BY detected_at DESC
                LIMIT 5
            """)
            recent_reactions = cursor.fetchall()

            conn.close()

            stats_text = f"""
📊 **إحصائيات البوت:**

🔄 **حالة المراقبة:** {"🟢 نشط" if self.is_monitoring else "🔴 متوقف"}
📺 **القناة المراقبة:** {getattr(self.monitoring_channel, 'title', 'لا توجد') if self.monitoring_channel else 'لا توجد'}

📈 **الإحصائيات:**
🎭 إجمالي التفاعلات المسجلة: {total_reactions}
💬 الرسائل التي تم تسجيل تفاعلات عليها: {unique_messages}

🏆 **أكثر الإيموجي تسجيلاً:**
"""

            for i, (emoji, count) in enumerate(top_emojis, 1):
                stats_text += f"{i}. {emoji} - {count} مرة\n"

            if recent_reactions:
                stats_text += "\n🕐 **آخر التفاعلات المسجلة:**\n"
                for msg_id, emoji, count, detected_at in recent_reactions:
                    stats_text += f"• {emoji} على الرسالة {msg_id} ({count}) - {detected_at}\n"

            stats_text += f"\n📅 **آخر تحديث:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

            await event.respond(stats_text)

        except Exception as e:
            await event.respond(f"❌ خطأ في الحصول على الإحصائيات: {str(e)}")

    async def handle_stop_monitoring(self, event):
        """معالجة أمر إيقاف المراقبة"""
        if not self.is_admin(event.sender_id):
            return

        if not self.is_monitoring:
            await event.respond("⚠️ المراقبة غير مفعلة حالياً.")
            return

        if self.scan_task:
            self.scan_task.cancel()
            self.scan_task = None

        if self.monitor_client:
            await self.monitor_client.disconnect()
            self.monitor_client = None

        self.is_monitoring = False
        self.monitoring_channel = None
        self.last_reactions = {}

        await event.respond("✅ تم إيقاف المراقبة بنجاح.")

    def is_admin(self, user_id: int) -> bool:
        """التحقق من صلاحيات المدير"""
        return user_id == self.admin_user_id

    async def send_welcome_message(self):
        """إرسال رسالة ترحيب للمدير"""
        try:
            message = """
🤖 **تم تشغيل بوت مراقبة التفاعلات الواقعي بنجاح!**

✅ **ما يمكن للبوت فعله:**
• مراقبة التفاعلات والإيموجي
• إحصائيات مفصلة للتفاعلات
• تحليل نشاط القناة
• معلومات مفصلة عن الرسائل

⚠️ **قيود Telegram:**
• لا يمكن عرض أسماء المتفاعلين في القنوات العامة
• بعض المعلومات قد تكون محدودة

استخدم /start للبدء.
            """
            await self.bot.send_message(self.admin_user_id, message)
        except Exception as e:
            print(f"لا يمكن إرسال رسالة الترحيب: {e}")

    async def run(self):
        """تشغيل البوت"""
        await self.start()
        await self.bot.run_until_disconnected()

async def main():
    print("🔄 بدء تشغيل بوت مراقبة التفاعلات الواقعي...")
    bot = RealisticReactionBot()
    await bot.run()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
