import asyncio
import logging
from datetime import datetime
from typing import Optional, List, Dict
import os
from dotenv import load_dotenv

from telethon import TelegramClient, events
from telethon.tl.types import (
    MessageReactions, ReactionCount, ReactionEmoji, 
    User, Channel, Chat, PeerChannel, PeerUser
)
from telethon.errors import SessionPasswordNeededError, FloodWaitError

from database import ReactionsDatabase

# تحميل متغيرات البيئة
load_dotenv()

class TelegramReactionMonitor:
    def __init__(self):
        self.api_id = int(os.getenv('API_ID'))
        self.api_hash = os.getenv('API_HASH')
        self.phone_number = os.getenv('PHONE_NUMBER')
        self.target_channel = os.getenv('TARGET_CHANNEL')
        self.admin_user_id = int(os.getenv('ADMIN_USER_ID', 0))
        
        # إعداد العميل
        self.client = TelegramClient('reaction_monitor', self.api_id, self.api_hash)
        
        # قاعدة البيانات
        self.db = ReactionsDatabase()
        
        # إعداد السجلات
        logging.basicConfig(
            level=getattr(logging, os.getenv('LOG_LEVEL', 'INFO')),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(os.getenv('LOG_FILE', 'bot.log'), encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # متغيرات التتبع
        self.monitored_channels = {}
        self.is_running = False
    
    async def start(self):
        """بدء تشغيل المراقب"""
        try:
            await self.client.start(phone=self.phone_number)
            self.logger.info("تم تسجيل الدخول بنجاح")
            
            # التحقق من صحة القناة المستهدفة
            if self.target_channel:
                await self.add_channel_to_monitor(self.target_channel)
            
            self.is_running = True
            self.logger.info("بدأ مراقبة التفاعلات...")
            
            # تسجيل معالجات الأحداث
            self.client.add_event_handler(self.handle_new_message, events.NewMessage)
            self.client.add_event_handler(self.handle_message_edited, events.MessageEdited)
            
            # بدء حلقة المراقبة
            await self.monitor_reactions()
            
        except SessionPasswordNeededError:
            self.logger.error("مطلوب كلمة مرور للجلسة (2FA)")
            password = input("أدخل كلمة مرور التحقق بخطوتين: ")
            await self.client.sign_in(password=password)
            await self.start()
            
        except Exception as e:
            self.logger.error(f"خطأ في بدء التشغيل: {e}")
    
    async def add_channel_to_monitor(self, channel_identifier: str):
        """إضافة قناة للمراقبة"""
        try:
            entity = await self.client.get_entity(channel_identifier)
            
            if hasattr(entity, 'id'):
                self.monitored_channels[entity.id] = {
                    'entity': entity,
                    'title': getattr(entity, 'title', 'Unknown'),
                    'username': getattr(entity, 'username', None)
                }
                self.logger.info(f"تمت إضافة القناة للمراقبة: {entity.title}")
                return True
            else:
                self.logger.error(f"لا يمكن العثور على القناة: {channel_identifier}")
                return False
                
        except Exception as e:
            self.logger.error(f"خطأ في إضافة القناة: {e}")
            return False
    
    async def handle_new_message(self, event):
        """معالجة الرسائل الجديدة"""
        try:
            if event.chat_id in self.monitored_channels:
                # حفظ الرسالة في قاعدة البيانات
                message_text = event.message.message or ""
                message_date = event.message.date
                
                message_table_id = self.db.add_message(
                    channel_id=event.chat_id,
                    message_id=event.message.id,
                    message_text=message_text,
                    message_date=message_date
                )
                
                if message_table_id:
                    self.logger.info(f"تم حفظ رسالة جديدة: {event.message.id}")
                
        except Exception as e:
            self.logger.error(f"خطأ في معالجة الرسالة الجديدة: {e}")
    
    async def handle_message_edited(self, event):
        """معالجة تعديل الرسائل (قد تتضمن تفاعلات جديدة)"""
        try:
            if event.chat_id in self.monitored_channels:
                await self.check_message_reactions(event.chat_id, event.message.id)

        except Exception as e:
            self.logger.error(f"خطأ في معالجة تعديل الرسالة: {e}")

    async def check_message_reactions(self, channel_id: int, message_id: int):
        """فحص تفاعلات رسالة معينة"""
        try:
            entity = self.monitored_channels[channel_id]['entity']
            message = await self.client.get_messages(entity, ids=message_id)

            if message and message.reactions:
                await self.process_message_reactions(channel_id, message)

        except Exception as e:
            self.logger.error(f"خطأ في فحص تفاعلات الرسالة: {e}")
    
    async def monitor_reactions(self):
        """مراقبة التفاعلات بشكل دوري"""
        while self.is_running:
            try:
                for channel_id in self.monitored_channels:
                    await self.scan_channel_reactions(channel_id)
                
                # انتظار قبل المسح التالي
                await asyncio.sleep(30)  # مسح كل 30 ثانية
                
            except FloodWaitError as e:
                self.logger.warning(f"تم تجاوز الحد المسموح، انتظار {e.seconds} ثانية")
                await asyncio.sleep(e.seconds)
                
            except Exception as e:
                self.logger.error(f"خطأ في مراقبة التفاعلات: {e}")
                await asyncio.sleep(60)  # انتظار دقيقة في حالة الخطأ
    
    async def scan_channel_reactions(self, channel_id: int, limit: int = 50):
        """مسح تفاعلات القناة"""
        try:
            entity = self.monitored_channels[channel_id]['entity']
            
            # الحصول على آخر الرسائل
            async for message in self.client.iter_messages(entity, limit=limit):
                if message.reactions:
                    await self.process_message_reactions(channel_id, message)
                    
        except Exception as e:
            self.logger.error(f"خطأ في مسح تفاعلات القناة {channel_id}: {e}")
    
    async def process_message_reactions(self, channel_id: int, message):
        """معالجة تفاعلات رسالة معينة"""
        try:
            # حفظ الرسالة أولاً
            message_table_id = self.db.add_message(
                channel_id=channel_id,
                message_id=message.id,
                message_text=message.message or "",
                message_date=message.date
            )
            
            if not message_table_id:
                return
            
            # معالجة كل تفاعل
            for reaction in message.reactions.results:
                if hasattr(reaction, 'reaction') and hasattr(reaction.reaction, 'emoticon'):
                    emoji = reaction.reaction.emoticon
                    
                    # الحصول على قائمة المتفاعلين لهذا الإيموجي
                    try:
                        participants = await self.client.get_message_reactions(
                            entity=channel_id,
                            msg_id=message.id,
                            reaction=reaction.reaction
                        )
                        
                        if participants and hasattr(participants, 'users'):
                            for user in participants.users:
                                await self.save_user_reaction(
                                    message_table_id=message_table_id,
                                    user=user,
                                    emoji=emoji,
                                    reaction_date=datetime.now()
                                )
                                
                    except Exception as e:
                        self.logger.debug(f"لا يمكن الحصول على تفاصيل التفاعل: {e}")
                        
        except Exception as e:
            self.logger.error(f"خطأ في معالجة تفاعلات الرسالة: {e}")
    
    async def save_user_reaction(self, message_table_id: int, user, emoji: str, reaction_date: datetime):
        """حفظ تفاعل المستخدم"""
        try:
            user_name = f"{user.first_name or ''} {user.last_name or ''}".strip()
            user_username = user.username or ""
            
            success = self.db.add_reaction(
                message_table_id=message_table_id,
                user_id=user.id,
                user_name=user_name,
                user_username=user_username,
                reaction_emoji=emoji,
                reaction_date=reaction_date
            )
            
            if success:
                self.logger.info(f"تم حفظ تفاعل: {user_name} - {emoji}")
                
                # إرسال تنبيه للمدير إذا كان محدد
                if self.admin_user_id:
                    await self.send_admin_notification(user_name, user_username, emoji)
                    
        except Exception as e:
            self.logger.error(f"خطأ في حفظ تفاعل المستخدم: {e}")
    
    async def send_admin_notification(self, user_name: str, user_username: str, emoji: str):
        """إرسال تنبيه للمدير"""
        try:
            username_text = f"@{user_username}" if user_username else "بدون معرف"
            message = f"🔔 تفاعل جديد!\n\n👤 المستخدم: {user_name}\n🆔 المعرف: {username_text}\n{emoji} التفاعل: {emoji}\n⏰ الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            await self.client.send_message(self.admin_user_id, message)
            
        except Exception as e:
            self.logger.debug(f"لا يمكن إرسال التنبيه للمدير: {e}")
    
    async def stop(self):
        """إيقاف المراقب"""
        self.is_running = False
        await self.client.disconnect()
        self.logger.info("تم إيقاف المراقب")
    
    async def get_channel_stats(self, channel_id: int) -> Dict:
        """الحصول على إحصائيات القناة"""
        try:
            top_reactors = self.db.get_top_reactors(channel_id, 10)
            popular_emojis = self.db.get_popular_emojis(channel_id, 10)
            
            return {
                'top_reactors': top_reactors,
                'popular_emojis': popular_emojis,
                'channel_info': self.monitored_channels.get(channel_id, {})
            }
            
        except Exception as e:
            self.logger.error(f"خطأ في الحصول على إحصائيات القناة: {e}")
            return {}
