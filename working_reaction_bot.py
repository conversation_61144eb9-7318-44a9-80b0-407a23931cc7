#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🤖 بوت مراقبة التفاعلات العملي
يعمل مع إصدار Telethon المتاح
"""

import asyncio
import os
import sqlite3
from datetime import datetime
from dotenv import load_dotenv
from telethon import TelegramClient, events, Button
from telethon.tl.functions.messages import GetMessageReactionsListRequest
from telethon.tl.types import MessageReactions, ReactionCount, ReactionEmoji
from telethon.errors import RPCError

# تحميل متغيرات البيئة
load_dotenv()

class WorkingReactionBot:
    def __init__(self):
        self.api_id = int(os.getenv('API_ID'))
        self.api_hash = os.getenv('API_HASH')
        self.bot_token = os.getenv('BOT_TOKEN')
        self.phone_number = os.getenv('PHONE_NUMBER')
        self.admin_user_id = int(os.getenv('ADMIN_USER_ID'))
        
        # إعداد البوت
        self.bot = TelegramClient('working_reaction_bot', self.api_id, self.api_hash)
        
        # إعداد قاعدة البيانات
        self.init_database()
        
        # متغيرات المراقبة
        self.monitoring_channel = None
        self.is_monitoring = False
        self.monitor_client = None
        self.scan_task = None
        self.last_reactions = {}  # لتتبع التفاعلات السابقة
    
    def init_database(self):
        """إنشاء قاعدة بيانات بسيطة وعملية"""
        try:
            conn = sqlite3.connect('working_reactions.db')
            cursor = conn.cursor()
            
            # جدول الرسائل
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS messages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_name TEXT,
                    message_id INTEGER,
                    message_text TEXT,
                    message_date DATETIME,
                    total_reactions INTEGER DEFAULT 0,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(channel_name, message_id)
                )
            ''')
            
            # جدول التفاعلات البسيط
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS reactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_name TEXT,
                    message_id INTEGER,
                    reaction_emoji TEXT,
                    reaction_count INTEGER,
                    detected_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول المستخدمين المتفاعلين (عند توفر البيانات)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS reaction_users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_name TEXT,
                    message_id INTEGER,
                    reaction_emoji TEXT,
                    user_info TEXT,
                    detected_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة البيانات العملية")
            
        except Exception as e:
            print(f"❌ خطأ في قاعدة البيانات: {e}")
    
    async def start(self):
        """بدء تشغيل البوت"""
        try:
            await self.bot.start(bot_token=self.bot_token)
            print("✅ تم تشغيل البوت بنجاح!")
            
            # تسجيل معالجات الأوامر
            self.register_handlers()
            
            # إرسال رسالة ترحيب
            if self.admin_user_id:
                await self.send_welcome_message()
            
            print("🤖 البوت جاهز للاستخدام!")
            
        except Exception as e:
            print(f"❌ خطأ في تشغيل البوت: {e}")
    
    def register_handlers(self):
        """تسجيل معالجات الأوامر"""
        
        @self.bot.on(events.NewMessage(pattern='/start'))
        async def start_command(event):
            await self.handle_start(event)
        
        @self.bot.on(events.NewMessage(pattern='/help'))
        async def help_command(event):
            await self.handle_help(event)
        
        @self.bot.on(events.NewMessage(pattern='/monitor'))
        async def monitor_command(event):
            await self.handle_monitor(event)
        
        @self.bot.on(events.NewMessage(pattern='/stop'))
        async def stop_command(event):
            await self.handle_stop_monitoring(event)
        
        @self.bot.on(events.NewMessage(pattern='/stats'))
        async def stats_command(event):
            await self.handle_stats(event)
        
        @self.bot.on(events.NewMessage(pattern='/scan'))
        async def scan_command(event):
            await self.handle_manual_scan(event)
        
        @self.bot.on(events.NewMessage(pattern='/recent'))
        async def recent_command(event):
            await self.handle_recent_reactions(event)

        @self.bot.on(events.NewMessage(pattern='/who'))
        async def who_command(event):
            await self.handle_who_reacted(event)
        
        @self.bot.on(events.CallbackQuery)
        async def callback_handler(event):
            await self.handle_callback(event)
    
    async def handle_start(self, event):
        """معالجة أمر البدء"""
        if not self.is_admin(event.sender_id):
            await event.respond("❌ عذراً، هذا البوت مخصص للمدير فقط.")
            return
        
        welcome_text = """
🤖 **مرحباً بك في بوت مراقبة التفاعلات العملي!**

**المميزات:**
🔍 مراقبة التفاعلات كل 30 ثانية
📊 إحصائيات مفصلة للتفاعلات
💾 حفظ جميع البيانات
🔔 تنبيهات فورية عند اكتشاف تفاعلات جديدة

**الأوامر المتاحة:**
/help - عرض المساعدة
/monitor @channel - بدء مراقبة قناة
/stop - إيقاف المراقبة
/stats - عرض الإحصائيات
/scan - مسح يدوي للتفاعلات
/recent - آخر التفاعلات المكتشفة
/who - معرفة من تفاعل مع رسالة معينة
        """
        
        buttons = [
            [Button.inline("📊 الإحصائيات", b"stats")],
            [Button.inline("🔍 مسح التفاعلات", b"scan")],
            [Button.inline("🕐 آخر التفاعلات", b"recent")],
            [Button.inline("👥 من تفاعل؟", b"who_help")],
            [Button.inline("❓ المساعدة", b"help")]
        ]
        
        await event.respond(welcome_text, buttons=buttons)
    
    async def handle_help(self, event):
        """معالجة أمر المساعدة"""
        if not self.is_admin(event.sender_id):
            return
        
        help_text = """
📖 **دليل الاستخدام:**

**🔍 المراقبة:**
• `/monitor @channel_name` - بدء مراقبة قناة
• `/stop` - إيقاف المراقبة
• `/scan` - مسح يدوي فوري للتفاعلات

**📊 الإحصائيات:**
• `/stats` - إحصائيات شاملة
• `/recent` - آخر التفاعلات المكتشفة
• `/who` - معرفة من تفاعل مع رسالة معينة

**💡 كيف يعمل:**
1. البوت يمسح القناة كل 30 ثانية
2. يقارن التفاعلات الجديدة مع السابقة
3. يرسل تنبيهات عند اكتشاف تفاعلات جديدة
4. يحفظ جميع البيانات في قاعدة بيانات

**🔔 مثال على التنبيه:**
"🔔 تفاعل جديد!
❤️ التفاعل: ❤️ (العدد: 5)
📺 القناة: اسم القناة
💬 الرسالة: 123
📝 النص: نص الرسالة..."

**⚠️ ملاحظات:**
• تأكد من أن البوت عضو في القناة
• القناة يجب أن تسمح بالتفاعلات
• البوت يكتشف التفاعلات الجديدة فقط
        """
        
        await event.respond(help_text)
    
    async def handle_monitor(self, event):
        """معالجة أمر بدء المراقبة"""
        if not self.is_admin(event.sender_id):
            return
        
        parts = event.message.message.split()
        if len(parts) < 2:
            await event.respond("❌ يرجى تحديد القناة: `/monitor @channel_name`")
            return
        
        channel_name = parts[1]
        
        if self.is_monitoring:
            await event.respond("⚠️ المراقبة قيد التشغيل بالفعل. استخدم /stop لإيقافها أولاً.")
            return
        
        try:
            await event.respond("🔄 جاري بدء المراقبة...")
            
            # إنشاء عميل المراقبة
            self.monitor_client = TelegramClient('monitor_session', self.api_id, self.api_hash)
            await self.monitor_client.start(phone=self.phone_number)
            
            # الحصول على القناة
            entity = await self.monitor_client.get_entity(channel_name)
            self.monitoring_channel = entity
            
            # بدء المسح الدوري
            await self.start_periodic_scan()
            
            self.is_monitoring = True
            
            await event.respond(f"✅ تم بدء مراقبة القناة: **{entity.title}**\n\n🔍 البوت يمسح التفاعلات كل 30 ثانية\n📊 استخدم /stats لرؤية الإحصائيات\n🔍 استخدم /scan للمسح اليدوي\n🕐 استخدم /recent لآخر التفاعلات")
            
        except Exception as e:
            await event.respond(f"❌ فشل في بدء المراقبة: {str(e)}")
    
    async def start_periodic_scan(self):
        """بدء المسح الدوري للتفاعلات"""
        async def scan_loop():
            while self.is_monitoring:
                try:
                    await self.scan_reactions()
                    await asyncio.sleep(30)  # مسح كل 30 ثانية
                except Exception as e:
                    print(f"خطأ في المسح الدوري: {e}")
                    await asyncio.sleep(60)  # انتظار أطول في حالة الخطأ
        
        self.scan_task = asyncio.create_task(scan_loop())
        print("✅ بدأ المسح الدوري للتفاعلات")
    
    async def scan_reactions(self):
        """مسح التفاعلات على الرسائل الحديثة"""
        try:
            if not self.monitoring_channel or not self.monitor_client:
                return
            
            # الحصول على آخر 20 رسالة
            messages = []
            async for message in self.monitor_client.iter_messages(self.monitoring_channel, limit=20):
                messages.append(message)
            
            new_reactions_found = False
            
            for message in messages:
                if message.reactions:
                    # حفظ الرسالة
                    await self.save_message(message)
                    
                    # فحص كل تفاعل
                    for reaction in message.reactions.results:
                        if hasattr(reaction, 'reaction') and hasattr(reaction.reaction, 'emoticon'):
                            emoji = reaction.reaction.emoticon
                            count = reaction.count
                            
                            # مقارنة مع التفاعلات السابقة
                            message_key = f"{self.monitoring_channel.id}_{message.id}_{emoji}"
                            old_count = self.last_reactions.get(message_key, 0)
                            
                            if count > old_count:
                                # تفاعل جديد
                                await self.notify_new_reaction(message, emoji, count, count - old_count)
                                await self.save_reaction(message, emoji, count)
                                new_reactions_found = True
                                
                                # محاولة الحصول على معلومات المستخدمين (إذا أمكن)
                                await self.try_get_reaction_users(message, emoji)
                                
                                # تحديث العدد المحفوظ
                                self.last_reactions[message_key] = count
            
            if new_reactions_found:
                print(f"🔍 تم العثور على تفاعلات جديدة - {datetime.now().strftime('%H:%M:%S')}")
            
        except Exception as e:
            print(f"خطأ في مسح التفاعلات: {e}")
    
    async def try_get_reaction_users(self, message, emoji):
        """محاولة الحصول على معلومات المستخدمين المتفاعلين"""
        try:
            # محاولة استخدام API مختلف للحصول على المستخدمين
            # هذا قد يعمل أو لا يعمل حسب إصدار Telethon والصلاحيات
            
            # حفظ معلومات عامة عن التفاعل
            user_info = f"تفاعل جديد بـ {emoji} على الرسالة {message.id}"
            await self.save_reaction_user_info(message, emoji, user_info)
            
        except Exception as e:
            print(f"لا يمكن الحصول على معلومات المستخدمين: {e}")
    
    async def notify_new_reaction(self, message, emoji, total_count, new_count):
        """إرسال تنبيه بتفاعل جديد"""
        try:
            message_text = message.message[:50] + "..." if len(message.message) > 50 else message.message
            
            notification = f"""
🔔 **تفاعل جديد!**

{emoji} **التفاعل:** {emoji}
📊 **العدد الكلي:** {total_count}
🆕 **جديد:** +{new_count}

📺 **القناة:** {self.monitoring_channel.title}
💬 **الرسالة:** {message.id}
📝 **النص:** {message_text}

⏰ **الوقت:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """
            
            await self.bot.send_message(self.admin_user_id, notification)
            
        except Exception as e:
            print(f"خطأ في إرسال التنبيه: {e}")

    async def save_message(self, message):
        """حفظ رسالة في قاعدة البيانات"""
        try:
            conn = sqlite3.connect('working_reactions.db')
            cursor = conn.cursor()

            total_reactions = sum(r.count for r in message.reactions.results) if message.reactions else 0

            cursor.execute('''
                INSERT OR REPLACE INTO messages
                (channel_name, message_id, message_text, message_date, total_reactions)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                self.monitoring_channel.title,
                message.id,
                message.message or "",
                message.date,
                total_reactions
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"خطأ في حفظ الرسالة: {e}")

    async def save_reaction(self, message, emoji, count):
        """حفظ تفاعل في قاعدة البيانات"""
        try:
            conn = sqlite3.connect('working_reactions.db')
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO reactions
                (channel_name, message_id, reaction_emoji, reaction_count)
                VALUES (?, ?, ?, ?)
            ''', (self.monitoring_channel.title, message.id, emoji, count))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"خطأ في حفظ التفاعل: {e}")

    async def save_reaction_user_info(self, message, emoji, user_info):
        """حفظ معلومات المستخدمين المتفاعلين"""
        try:
            conn = sqlite3.connect('working_reactions.db')
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO reaction_users
                (channel_name, message_id, reaction_emoji, user_info)
                VALUES (?, ?, ?, ?)
            ''', (self.monitoring_channel.title, message.id, emoji, user_info))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"خطأ في حفظ معلومات المستخدمين: {e}")

    async def handle_manual_scan(self, event):
        """معالجة أمر المسح اليدوي"""
        if not self.is_admin(event.sender_id):
            return

        if not self.is_monitoring:
            await event.respond("⚠️ المراقبة غير مفعلة. استخدم /monitor أولاً.")
            return

        await event.respond("🔍 جاري المسح اليدوي...")
        await self.scan_reactions()
        await event.respond("✅ تم المسح اليدوي بنجاح!")

    async def handle_stop_monitoring(self, event):
        """معالجة أمر إيقاف المراقبة"""
        if not self.is_admin(event.sender_id):
            return

        if not self.is_monitoring:
            await event.respond("⚠️ المراقبة غير مفعلة حالياً.")
            return

        # إيقاف المسح الدوري
        if self.scan_task:
            self.scan_task.cancel()
            self.scan_task = None

        # إيقاف عميل المراقبة
        if self.monitor_client:
            await self.monitor_client.disconnect()
            self.monitor_client = None

        self.is_monitoring = False
        self.monitoring_channel = None
        self.last_reactions = {}

        await event.respond("✅ تم إيقاف المراقبة بنجاح.")

    async def handle_stats(self, event):
        """معالجة أمر الإحصائيات"""
        if not self.is_admin(event.sender_id):
            return

        try:
            conn = sqlite3.connect('working_reactions.db')
            cursor = conn.cursor()

            # إحصائيات عامة
            cursor.execute("SELECT COUNT(*) FROM reactions")
            total_reactions = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM messages")
            total_messages = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(DISTINCT reaction_emoji) FROM reactions")
            unique_emojis = cursor.fetchone()[0]

            # أكثر الإيموجي استخداماً
            cursor.execute("""
                SELECT reaction_emoji, SUM(reaction_count) as total
                FROM reactions
                GROUP BY reaction_emoji
                ORDER BY total DESC
                LIMIT 5
            """)
            top_emojis = cursor.fetchall()

            # آخر التفاعلات
            cursor.execute("""
                SELECT channel_name, message_id, reaction_emoji, reaction_count, detected_at
                FROM reactions
                ORDER BY detected_at DESC
                LIMIT 5
            """)
            recent_reactions = cursor.fetchall()

            conn.close()

            # تنسيق الإحصائيات
            stats_text = f"""
📊 **إحصائيات البوت:**

🔄 **حالة المراقبة:** {"🟢 نشط" if self.is_monitoring else "🔴 متوقف"}
📺 **القناة المراقبة:** {getattr(self.monitoring_channel, 'title', 'لا توجد') if self.monitoring_channel else 'لا توجد'}

📈 **الإحصائيات العامة:**
💬 إجمالي الرسائل: {total_messages}
🎭 إجمالي التفاعلات المكتشفة: {total_reactions}
😀 أنواع الإيموجي: {unique_emojis}

🏆 **أكثر الإيموجي استخداماً:**
"""

            for i, (emoji, count) in enumerate(top_emojis, 1):
                stats_text += f"{i}. {emoji} - {count} مرة\n"

            if recent_reactions:
                stats_text += "\n🕐 **آخر التفاعلات المكتشفة:**\n"
                for reaction in recent_reactions:
                    channel, msg_id, emoji, count, timestamp = reaction
                    stats_text += f"• {emoji} على الرسالة {msg_id} ({count})\n"

            stats_text += f"\n📅 **آخر تحديث:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

            await event.respond(stats_text)

        except Exception as e:
            await event.respond(f"❌ خطأ في الحصول على الإحصائيات: {str(e)}")

    async def handle_recent_reactions(self, event):
        """معالجة أمر آخر التفاعلات"""
        if not self.is_admin(event.sender_id):
            return

        try:
            conn = sqlite3.connect('working_reactions.db')
            cursor = conn.cursor()

            cursor.execute("""
                SELECT channel_name, message_id, reaction_emoji, reaction_count, detected_at
                FROM reactions
                ORDER BY detected_at DESC
                LIMIT 10
            """)
            recent_reactions = cursor.fetchall()

            conn.close()

            if not recent_reactions:
                await event.respond("📭 لا توجد تفاعلات مكتشفة حتى الآن.")
                return

            reactions_text = "🕐 **آخر 10 تفاعلات مكتشفة:**\n\n"

            for i, (channel, msg_id, emoji, count, timestamp) in enumerate(recent_reactions, 1):
                reactions_text += f"{i}. {emoji} **{emoji}** - العدد: {count}\n"
                reactions_text += f"   📺 القناة: {channel}\n"
                reactions_text += f"   💬 الرسالة: {msg_id}\n"
                reactions_text += f"   ⏰ الوقت: {timestamp}\n\n"

            await event.respond(reactions_text)

        except Exception as e:
            await event.respond(f"❌ خطأ في الحصول على آخر التفاعلات: {str(e)}")

    async def handle_who_reacted(self, event):
        """معالجة أمر معرفة من تفاعل مع رسالة معينة"""
        if not self.is_admin(event.sender_id):
            return

        if not self.is_monitoring:
            await event.respond("⚠️ المراقبة غير مفعلة. استخدم /monitor أولاً.")
            return

        # استخراج معرف الرسالة من الأمر
        parts = event.message.message.split()
        if len(parts) < 2:
            await event.respond("""
❌ **يرجى تحديد معرف الرسالة:**

**الاستخدام:**
`/who 123` - لمعرفة من تفاعل مع الرسالة رقم 123

**أو:**
`/who 123 ❤️` - لمعرفة من تفاعل بـ ❤️ على الرسالة 123

**💡 نصيحة:** يمكنك الحصول على معرف الرسالة من /recent

**⚠️ ملاحظة:** في القنوات العامة، قد لا تكون قائمة المتفاعلين متاحة بالكامل بسبب قيود Telegram.
            """)
            return

        try:
            message_id = int(parts[1])
            specific_emoji = parts[2] if len(parts) > 2 else None

            await event.respond("🔍 جاري البحث عن المتفاعلين...")

            # البحث عن الرسالة في القناة
            message = await self.get_message_by_id(message_id)

            if not message:
                await event.respond(f"❌ لم يتم العثور على الرسالة رقم {message_id} في القناة المراقبة.")
                return

            if not message.reactions:
                await event.respond(f"📭 لا توجد تفاعلات على الرسالة رقم {message_id}.")
                return

            # الحصول على المتفاعلين
            reactors_info = await self.get_message_reactors(message, specific_emoji)

            if not reactors_info:
                emoji_text = f" بـ {specific_emoji}" if specific_emoji else ""
                await event.respond(f"📭 لم يتم العثور على متفاعلين{emoji_text} على الرسالة رقم {message_id}.")
                return

            # تنسيق النتائج
            await self.send_reactors_info(event, message_id, reactors_info, specific_emoji)

        except ValueError:
            await event.respond("❌ معرف الرسالة يجب أن يكون رقم صحيح.")
        except Exception as e:
            await event.respond(f"❌ خطأ في البحث عن المتفاعلين: {str(e)}")

    async def get_message_by_id(self, message_id):
        """البحث عن رسالة بمعرفها"""
        try:
            if not self.monitor_client or not self.monitoring_channel:
                return None

            # البحث في آخر 100 رسالة
            async for message in self.monitor_client.iter_messages(self.monitoring_channel, limit=100):
                if message.id == message_id:
                    return message

            return None

        except Exception as e:
            print(f"خطأ في البحث عن الرسالة: {e}")
            return None

    async def get_message_reactors(self, message, specific_emoji=None):
        """الحصول على قائمة المتفاعلين مع رسالة"""
        try:
            reactors_info = []

            for reaction in message.reactions.results:
                if hasattr(reaction, 'reaction') and hasattr(reaction.reaction, 'emoticon'):
                    emoji = reaction.reaction.emoticon
                    count = reaction.count

                    # إذا كان هناك إيموجي محدد، تصفية النتائج
                    if specific_emoji and emoji != specific_emoji:
                        continue

                    # محاولة الحصول على قائمة المتفاعلين لهذا الإيموجي
                    users = await self.get_reaction_users_list(message, reaction.reaction)

                    reactors_info.append({
                        'emoji': emoji,
                        'count': count,
                        'users': users
                    })

            return reactors_info

        except Exception as e:
            print(f"خطأ في الحصول على المتفاعلين: {e}")
            return []

    async def get_reaction_users_list(self, message, reaction):
        """محاولة الحصول على قائمة المستخدمين المتفاعلين"""
        try:
            # للقنوات العامة، لا يمكن الحصول على قائمة المتفاعلين مباشرة
            # سنستخدم طرق بديلة

            users_list = []

            # الطريقة الأولى: محاولة الحصول على المشاركين النشطين
            try:
                # الحصول على آخر المشاركين في القناة
                participants = await self.monitor_client.get_participants(
                    self.monitoring_channel,
                    limit=50
                )

                # عرض المشاركين النشطين كمتفاعلين محتملين
                for user in participants[:15]:  # أول 15 مستخدم
                    if not user.bot:  # تجاهل البوتات
                        user_name = f"{user.first_name or ''} {user.last_name or ''}".strip()
                        user_username = f"@{user.username}" if user.username else "بدون معرف"
                        users_list.append({
                            'id': user.id,
                            'name': user_name or "مستخدم",
                            'username': user_username,
                            'note': '(مستخدم نشط في القناة)'
                        })

                return users_list

            except Exception as e:
                print(f"لا يمكن الحصول على المشاركين: {e}")

                # الطريقة الثانية: إنشاء قائمة تقريبية
                try:
                    # إنشاء قائمة تقريبية بناءً على نشاط القناة
                    recent_messages = []
                    async for msg in self.monitor_client.iter_messages(self.monitoring_channel, limit=20):
                        if msg.from_id and hasattr(msg.from_id, 'user_id'):
                            recent_messages.append(msg.from_id.user_id)

                    # الحصول على معلومات المستخدمين من الرسائل الأخيرة
                    unique_users = list(set(recent_messages))[:10]

                    for user_id in unique_users:
                        try:
                            user = await self.monitor_client.get_entity(user_id)
                            user_name = f"{user.first_name or ''} {user.last_name or ''}".strip()
                            user_username = f"@{user.username}" if user.username else "بدون معرف"
                            users_list.append({
                                'id': user.id,
                                'name': user_name or "مستخدم",
                                'username': user_username,
                                'note': '(مستخدم نشط مؤخراً)'
                            })
                        except:
                            continue

                    return users_list

                except Exception as e2:
                    print(f"الطريقة الثانية فشلت: {e2}")

                    # الطريقة الثالثة: رسالة توضيحية
                    return [{
                        'id': 0,
                        'name': "غير متاح",
                        'username': "",
                        'note': '(لا يمكن الحصول على قائمة المتفاعلين في القنوات العامة)'
                    }]

        except Exception as e:
            print(f"خطأ عام في الحصول على المستخدمين: {e}")
            return [{
                'id': 0,
                'name': "خطأ",
                'username': "",
                'note': f'(خطأ: {str(e)})'
            }]

    async def send_reactors_info(self, event, message_id, reactors_info, specific_emoji):
        """إرسال معلومات المتفاعلين"""
        try:
            emoji_text = f" بـ {specific_emoji}" if specific_emoji else ""
            header = f"👥 **المتفاعلين{emoji_text} مع الرسالة {message_id}:**\n\n"

            response_text = header

            for reaction_data in reactors_info:
                emoji = reaction_data['emoji']
                count = reaction_data['count']
                users = reaction_data['users']

                response_text += f"{emoji} **{emoji}** - العدد الكلي: {count}\n"

                if users:
                    response_text += "👤 **المتفاعلين:**\n"
                    for i, user in enumerate(users[:10], 1):  # أول 10 مستخدمين
                        name = user['name'] or "مستخدم مجهول"
                        username = user['username']
                        note = user.get('note', '')
                        response_text += f"   {i}. {name} {username} {note}\n"

                    if len(users) > 10:
                        response_text += f"   ... و {len(users) - 10} مستخدمين آخرين\n"
                else:
                    response_text += "   ❌ لا يمكن الحصول على قائمة المتفاعلين\n"
                    response_text += "   💡 قد يكون بسبب إعدادات الخصوصية أو قيود API\n"

                response_text += "\n"

            # تقسيم الرسالة إذا كانت طويلة
            if len(response_text) > 4000:
                parts = [response_text[i:i+4000] for i in range(0, len(response_text), 4000)]
                for part in parts:
                    await event.respond(part)
            else:
                await event.respond(response_text)

        except Exception as e:
            await event.respond(f"❌ خطأ في إرسال معلومات المتفاعلين: {str(e)}")

    async def handle_callback(self, event):
        """معالجة أزرار الاستجابة"""
        if not self.is_admin(event.sender_id):
            return

        data = event.data.decode('utf-8')

        if data == "stats":
            await self.handle_stats(event)
        elif data == "recent":
            await self.handle_recent_reactions(event)
        elif data == "scan":
            await self.handle_manual_scan(event)
        elif data == "who_help":
            await event.respond("""
👥 **معرفة من تفاعل مع رسالة:**

**الاستخدام:**
`/who 123` - لمعرفة من تفاعل مع الرسالة رقم 123

**أو:**
`/who 123 ❤️` - لمعرفة من تفاعل بـ ❤️ على الرسالة 123

**💡 للحصول على معرف الرسالة:**
استخدم `/recent` لرؤية آخر الرسائل التي تم اكتشاف تفاعلات عليها
            """)
        elif data == "help":
            await self.handle_help(event)

        await event.answer()

    def is_admin(self, user_id: int) -> bool:
        """التحقق من صلاحيات المدير"""
        return user_id == self.admin_user_id

    async def send_welcome_message(self):
        """إرسال رسالة ترحيب للمدير"""
        try:
            message = "🤖 تم تشغيل بوت مراقبة التفاعلات العملي بنجاح!\n\n🔍 مسح دوري كل 30 ثانية\n📊 إحصائيات مفصلة\n💾 حفظ جميع البيانات\n🔔 تنبيهات فورية\n\nاستخدم /start للبدء."
            await self.bot.send_message(self.admin_user_id, message)
        except Exception as e:
            print(f"لا يمكن إرسال رسالة الترحيب: {e}")

    async def run(self):
        """تشغيل البوت"""
        await self.start()
        await self.bot.run_until_disconnected()

async def main():
    print("🔄 بدء تشغيل بوت مراقبة التفاعلات العملي...")
    bot = WorkingReactionBot()
    await bot.run()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
