#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🧪 اختبار بسيط للبوت
"""

import asyncio
import os
from dotenv import load_dotenv
from telethon import TelegramClient, events

# تحميل متغيرات البيئة
load_dotenv()

async def main():
    print("🔄 بدء اختبار البوت...")
    
    # قراءة البيانات
    api_id = int(os.getenv('API_ID'))
    api_hash = os.getenv('API_HASH')
    bot_token = os.getenv('BOT_TOKEN')
    admin_user_id = int(os.getenv('ADMIN_USER_ID'))
    
    print(f"✅ API_ID: {api_id}")
    print(f"✅ API_HASH: {api_hash[:10]}...")
    print(f"✅ BOT_TOKEN: {bot_token[:10]}...")
    print(f"✅ ADMIN_USER_ID: {admin_user_id}")
    
    # إنشاء البوت
    bot = TelegramClient('test_bot', api_id, api_hash)
    
    @bot.on(events.NewMessage(pattern='/start'))
    async def start_handler(event):
        if event.sender_id == admin_user_id:
            await event.respond("🎉 البوت يعمل بنجاح!\n\nالأوامر المتاحة:\n/test - اختبار\n/ping - فحص الاتصال")
        else:
            await event.respond("❌ عذراً، هذا البوت مخصص للمدير فقط.")
    
    @bot.on(events.NewMessage(pattern='/test'))
    async def test_handler(event):
        if event.sender_id == admin_user_id:
            await event.respond("✅ الاختبار نجح! البوت يعمل بشكل صحيح.")
    
    @bot.on(events.NewMessage(pattern='/ping'))
    async def ping_handler(event):
        if event.sender_id == admin_user_id:
            await event.respond("🏓 Pong! البوت متصل.")
    
    try:
        print("🚀 تشغيل البوت...")
        await bot.start(bot_token=bot_token)
        print("✅ تم تشغيل البوت بنجاح!")
        print(f"📱 ابحث عن البوت في التليجرام وأرسل /start")
        print("🛑 اضغط Ctrl+C لإيقاف البوت")
        
        await bot.run_until_disconnected()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    asyncio.run(main())
