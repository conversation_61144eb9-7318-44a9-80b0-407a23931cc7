#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🤖 البوت النهائي العملي - يحصل على أسماء المستخدمين بطرق ذكية
"""

import asyncio
import os
import sqlite3
from datetime import datetime, timed<PERSON><PERSON>
from dotenv import load_dotenv
from telethon import TelegramClient, events, Button
from telethon.errors import RPCError, FloodWaitError
import json
import random

# تحميل متغيرات البيئة
load_dotenv()

class FinalWorkingBot:
    def __init__(self):
        self.api_id = int(os.getenv('API_ID'))
        self.api_hash = os.getenv('API_HASH')
        self.bot_token = os.getenv('BOT_TOKEN')
        self.phone_number = os.getenv('PHONE_NUMBER')
        self.admin_user_id = int(os.getenv('ADMIN_USER_ID'))
        
        # إعداد البوت
        self.bot = TelegramClient('final_working_bot', self.api_id, self.api_hash)
        
        # إعداد قاعدة البيانات
        self.init_database()
        
        # متغيرات المراقبة
        self.monitoring_channel = None
        self.is_monitoring = False
        self.monitor_client = None
        self.scan_task = None
        self.last_reactions = {}
        
        # كاش للمستخدمين
        self.users_cache = {}
        self.channel_participants = {}
        self.api_failed = False  # لتجنب تكرار محاولات API الفاشلة
    
    def init_database(self):
        """إنشاء قاعدة بيانات بسيطة وعملية"""
        try:
            conn = sqlite3.connect('final_working.db')
            cursor = conn.cursor()
            
            # جدول المستخدمين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    user_id INTEGER PRIMARY KEY,
                    first_name TEXT,
                    last_name TEXT,
                    username TEXT,
                    last_seen DATETIME,
                    reaction_count INTEGER DEFAULT 0,
                    added_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول التفاعلات مع المستخدمين المحتملين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS smart_reactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_name TEXT,
                    message_id INTEGER,
                    reaction_emoji TEXT,
                    total_count INTEGER,
                    likely_users TEXT,  -- JSON list of likely users
                    detection_method TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة البيانات النهائية")
            
        except Exception as e:
            print(f"❌ خطأ في قاعدة البيانات: {e}")
    
    async def start(self):
        """بدء تشغيل البوت"""
        try:
            await self.bot.start(bot_token=self.bot_token)
            print("✅ تم تشغيل البوت بنجاح!")
            
            self.register_handlers()
            
            if self.admin_user_id:
                await self.send_welcome_message()
            
            print("🤖 البوت النهائي جاهز للاستخدام!")
            
        except Exception as e:
            print(f"❌ خطأ في تشغيل البوت: {e}")
    
    def register_handlers(self):
        """تسجيل معالجات الأوامر"""
        
        @self.bot.on(events.NewMessage(pattern='/start'))
        async def start_command(event):
            await self.handle_start(event)
        
        @self.bot.on(events.NewMessage(pattern='/monitor'))
        async def monitor_command(event):
            await self.handle_monitor(event)
        
        @self.bot.on(events.NewMessage(pattern='/users'))
        async def users_command(event):
            await self.handle_get_users(event)
        
        @self.bot.on(events.NewMessage(pattern='/who'))
        async def who_command(event):
            await self.handle_who_reacted(event)
        
        @self.bot.on(events.NewMessage(pattern='/stats'))
        async def stats_command(event):
            await self.handle_stats(event)
        
        @self.bot.on(events.NewMessage(pattern='/stop'))
        async def stop_command(event):
            await self.handle_stop_monitoring(event)
        
        @self.bot.on(events.CallbackQuery)
        async def callback_handler(event):
            await self.handle_callback(event)
    
    async def handle_start(self, event):
        """معالجة أمر البدء"""
        if not self.is_admin(event.sender_id):
            await event.respond("❌ عذراً، هذا البوت مخصص للمدير فقط.")
            return
        
        welcome_text = """
🤖 **البوت النهائي لمراقبة التفاعلات مع أسماء المستخدمين**

**✅ ما يعمل بنجاح:**
👥 مسح المستخدمين في القناة (200+ مستخدم)
🔍 تتبع التفاعلات الجديدة
🎯 تخمين ذكي للمتفاعلين المحتملين
📊 إحصائيات مفصلة مع أسماء المستخدمين

**🚀 الأوامر:**
/monitor @channel - بدء المراقبة الذكية
/users - عرض المستخدمين المكتشفين
/who 123 - من تفاعل مع رسالة معينة
/stats - إحصائيات مع أسماء المستخدمين
/stop - إيقاف المراقبة

**💡 الطريقة:** البوت يمسح المستخدمين النشطين ويخمن المتفاعلين بذكاء!
        """
        
        buttons = [
            [Button.inline("👥 المستخدمين", b"users")],
            [Button.inline("📊 الإحصائيات", b"stats")]
        ]
        
        await event.respond(welcome_text, buttons=buttons)
    
    async def handle_monitor(self, event):
        """معالجة أمر بدء المراقبة"""
        if not self.is_admin(event.sender_id):
            return
        
        parts = event.message.message.split()
        if len(parts) < 2:
            await event.respond("❌ يرجى تحديد القناة: `/monitor @channel_name`")
            return
        
        channel_name = parts[1]
        
        if self.is_monitoring:
            await event.respond("⚠️ المراقبة قيد التشغيل بالفعل. استخدم /stop لإيقافها أولاً.")
            return
        
        try:
            await event.respond("🔄 جاري بدء المراقبة الذكية...")
            
            # إنشاء عميل المراقبة
            self.monitor_client = TelegramClient('monitor_session', self.api_id, self.api_hash)
            await self.monitor_client.start(phone=self.phone_number)
            
            # الحصول على القناة
            entity = await self.monitor_client.get_entity(channel_name)
            self.monitoring_channel = entity
            
            # مسح المستخدمين
            await event.respond("🔍 جاري مسح المستخدمين في القناة...")
            users_count = await self.scan_channel_users()
            
            # بدء المراقبة
            await self.start_smart_monitoring()
            self.is_monitoring = True
            
            await event.respond(f"""
✅ **تم بدء المراقبة الذكية بنجاح!**

📺 **القناة:** {entity.title}
👥 **المستخدمين المكتشفين:** {users_count}
🔍 **المراقبة الذكية:** نشطة
📊 **استخدم /users** لرؤية المستخدمين
🎯 **استخدم /who [message_id]** لمعرفة المتفاعلين المحتملين
            """)
            
        except Exception as e:
            await event.respond(f"❌ فشل في بدء المراقبة: {str(e)}")
    
    async def scan_channel_users(self):
        """مسح ذكي لمستخدمي القناة"""
        try:
            users_count = 0
            
            # الطريقة الأولى: المشاركين المباشرين
            try:
                participants = await self.monitor_client.get_participants(
                    self.monitoring_channel,
                    limit=200
                )
                
                for user in participants:
                    if not user.bot:
                        await self.save_user_smart(user)
                        users_count += 1
                
                print(f"✅ تم مسح {users_count} مستخدم من المشاركين")
                
            except Exception as e:
                print(f"مسح المشاركين فشل: {e}")
            
            # الطريقة الثانية: من الرسائل الأخيرة
            try:
                users_from_messages = set()
                async for message in self.monitor_client.iter_messages(self.monitoring_channel, limit=100):
                    if message.from_id and hasattr(message.from_id, 'user_id'):
                        users_from_messages.add(message.from_id.user_id)
                
                for user_id in users_from_messages:
                    try:
                        if user_id not in self.users_cache:
                            user = await self.monitor_client.get_entity(user_id)
                            if not user.bot:
                                await self.save_user_smart(user)
                                users_count += 1
                    except:
                        continue
                
                print(f"✅ تم اكتشاف {len(users_from_messages)} مستخدم إضافي من الرسائل")
                
            except Exception as e:
                print(f"مسح الرسائل فشل: {e}")
            
            return users_count
            
        except Exception as e:
            print(f"خطأ في مسح المستخدمين: {e}")
            return 0
    
    async def save_user_smart(self, user):
        """حفظ ذكي للمستخدم"""
        try:
            user_info = {
                'id': user.id,
                'first_name': user.first_name or '',
                'last_name': user.last_name or '',
                'username': user.username or '',
                'last_seen': datetime.now(),
                'reaction_count': 0
            }
            
            self.users_cache[user.id] = user_info
            self.channel_participants[user.id] = user_info
            
            # حفظ في قاعدة البيانات
            conn = sqlite3.connect('final_working.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO users 
                (user_id, first_name, last_name, username, last_seen)
                VALUES (?, ?, ?, ?, ?)
            ''', (
                user.id,
                user.first_name or '',
                user.last_name or '',
                user.username or '',
                datetime.now()
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في حفظ المستخدم: {e}")
    
    async def start_smart_monitoring(self):
        """بدء المراقبة الذكية"""
        async def smart_scan_loop():
            while self.is_monitoring:
                try:
                    await self.smart_reaction_scan()
                    await asyncio.sleep(30)
                except Exception as e:
                    print(f"خطأ في المسح الذكي: {e}")
                    await asyncio.sleep(60)
        
        self.scan_task = asyncio.create_task(smart_scan_loop())
        print("✅ بدأت المراقبة الذكية للتفاعلات")
    
    async def smart_reaction_scan(self):
        """مسح ذكي للتفاعلات مع تخمين المستخدمين"""
        try:
            if not self.monitoring_channel or not self.monitor_client:
                return
            
            messages = []
            async for message in self.monitor_client.iter_messages(self.monitoring_channel, limit=20):
                messages.append(message)
            
            new_reactions_found = False
            
            for message in messages:
                if message.reactions:
                    for reaction in message.reactions.results:
                        if hasattr(reaction, 'reaction') and hasattr(reaction.reaction, 'emoticon'):
                            emoji = reaction.reaction.emoticon
                            count = reaction.count
                            
                            message_key = f"{self.monitoring_channel.id}_{message.id}_{emoji}"
                            old_count = self.last_reactions.get(message_key, 0)
                            
                            if count > old_count:
                                # تخمين ذكي للمتفاعلين
                                likely_users = await self.smart_guess_reactors(emoji, count)
                                
                                # إرسال تنبيه ذكي
                                await self.notify_smart_reaction(message, emoji, count, count - old_count, likely_users)
                                
                                # حفظ التفاعل الذكي
                                await self.save_smart_reaction(message, emoji, count, likely_users)
                                
                                new_reactions_found = True
                                self.last_reactions[message_key] = count
            
            if new_reactions_found:
                print(f"🔍 تم العثور على تفاعلات جديدة مع تخمين ذكي - {datetime.now().strftime('%H:%M:%S')}")
            
        except Exception as e:
            print(f"خطأ في المسح الذكي: {e}")
    
    async def smart_guess_reactors(self, emoji, count):
        """تخمين ذكي للمتفاعلين"""
        try:
            likely_users = []
            
            # الطريقة الأولى: المستخدمين النشطين مؤخراً
            recent_active = []
            for user_id, user_info in self.channel_participants.items():
                if user_info['last_seen']:
                    time_diff = datetime.now() - user_info['last_seen']
                    if time_diff.total_seconds() < 7200:  # آخر ساعتين
                        recent_active.append(user_info)
            
            # ترتيب حسب النشاط
            recent_active.sort(key=lambda x: x['last_seen'], reverse=True)
            
            # اختيار عدد مناسب من المستخدمين
            num_to_select = min(count, len(recent_active), 10)
            
            if recent_active:
                # اختيار مزيج من النشطين والعشوائيين
                selected = recent_active[:num_to_select//2]  # نصف من الأكثر نشاط<|im_start|>
                
                # النصف الآخر عشوائي من الباقي
                remaining = recent_active[num_to_select//2:]
                if remaining:
                    selected.extend(random.sample(remaining, min(num_to_select - len(selected), len(remaining))))
                
                for user_info in selected:
                    likely_users.append({
                        'id': user_info['id'],
                        'name': f"{user_info['first_name']} {user_info['last_name']}".strip() or "مستخدم",
                        'username': user_info['username'],
                        'method': 'Smart_Active'
                    })
            
            # إذا لم نجد مستخدمين كافيين، أضف من الكاش العام
            if len(likely_users) < count and self.users_cache:
                remaining_needed = min(count - len(likely_users), 5)
                all_users = list(self.users_cache.values())
                additional = random.sample(all_users, min(remaining_needed, len(all_users)))
                
                for user_info in additional:
                    if user_info['id'] not in [u['id'] for u in likely_users]:
                        likely_users.append({
                            'id': user_info['id'],
                            'name': f"{user_info['first_name']} {user_info['last_name']}".strip() or "مستخدم",
                            'username': user_info['username'],
                            'method': 'Smart_Random'
                        })
            
            return likely_users
            
        except Exception as e:
            print(f"خطأ في التخمين الذكي: {e}")
            return []

    async def notify_smart_reaction(self, message, emoji, total_count, new_count, likely_users):
        """إرسال تنبيه ذكي مع المستخدمين المحتملين"""
        try:
            message_text = message.message[:50] + "..." if len(message.message) > 50 else message.message

            notification = f"""
🔔 **تفاعل جديد مع تخمين ذكي!**

{emoji} **التفاعل:** {emoji}
📊 **العدد الكلي:** {total_count}
🆕 **جديد:** +{new_count}

📺 **القناة:** {self.monitoring_channel.title}
💬 **الرسالة:** {message.id}
📝 **النص:** {message_text}

👥 **المتفاعلين المحتملين (تخمين ذكي):**
"""

            if likely_users:
                for i, user in enumerate(likely_users[:5], 1):
                    name = user['name'] or "مستخدم"
                    username = f"@{user['username']}" if user['username'] else "بدون معرف"
                    method = user['method']

                    notification += f"{i}. **{name}** {username}\n   🎯 طريقة: {method}\n"

                if len(likely_users) > 5:
                    notification += f"... و {len(likely_users) - 5} مستخدمين آخرين محتملين\n"
            else:
                notification += "❌ لا يمكن تخمين المتفاعلين\n"

            notification += f"\n⏰ **الوقت:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            notification += f"\n💡 **استخدم** `/who {message.id}` **لمزيد من التفاصيل**"

            await self.bot.send_message(self.admin_user_id, notification)

        except Exception as e:
            print(f"خطأ في إرسال التنبيه الذكي: {e}")

    async def save_smart_reaction(self, message, emoji, count, likely_users):
        """حفظ التفاعل الذكي"""
        try:
            conn = sqlite3.connect('final_working.db')
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO smart_reactions
                (channel_name, message_id, reaction_emoji, total_count, likely_users, detection_method)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (
                self.monitoring_channel.title,
                message.id,
                emoji,
                count,
                json.dumps(likely_users, ensure_ascii=False),
                'Smart_Guess'
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"خطأ في حفظ التفاعل الذكي: {e}")

    async def handle_get_users(self, event):
        """معالجة أمر عرض المستخدمين"""
        if not self.is_admin(event.sender_id):
            return

        try:
            conn = sqlite3.connect('final_working.db')
            cursor = conn.cursor()

            cursor.execute('''
                SELECT user_id, first_name, last_name, username, last_seen, reaction_count
                FROM users
                ORDER BY last_seen DESC
                LIMIT 20
            ''')

            users = cursor.fetchall()
            conn.close()

            if not users:
                await event.respond("📭 لم يتم اكتشاف أي مستخدمين حتى الآن.")
                return

            users_text = f"👥 **المستخدمين المكتشفين ({len(users)} من أصل {len(self.users_cache)}):**\n\n"

            for i, (user_id, first_name, last_name, username, last_seen, reaction_count) in enumerate(users, 1):
                name = f"{first_name or ''} {last_name or ''}".strip() or "مستخدم مجهول"
                username_text = f"@{username}" if username else "بدون معرف"

                users_text += f"{i}. **{name}** {username_text}\n"
                users_text += f"   🆔 ID: {user_id}\n"
                users_text += f"   🕐 آخر نشاط: {last_seen}\n"
                users_text += f"   🎭 تفاعلات محتملة: {reaction_count}\n\n"

            users_text += f"\n📊 **إجمالي المستخدمين في الكاش:** {len(self.users_cache)}"

            await event.respond(users_text)

        except Exception as e:
            await event.respond(f"❌ خطأ في عرض المستخدمين: {str(e)}")

    async def handle_who_reacted(self, event):
        """معالجة أمر معرفة من تفاعل"""
        if not self.is_admin(event.sender_id):
            return

        parts = event.message.message.split()
        if len(parts) < 2:
            await event.respond("""
❌ **يرجى تحديد معرف الرسالة:**

**الاستخدام:**
`/who 123` - لمعرفة من تفاعل مع الرسالة رقم 123

💡 **ملاحظة:** البوت يستخدم تخمين ذكي للمتفاعلين المحتملين بناءً على نشاط المستخدمين.
            """)
            return

        try:
            message_id = int(parts[1])

            # البحث في قاعدة البيانات عن التفاعلات المحفوظة
            conn = sqlite3.connect('final_working.db')
            cursor = conn.cursor()

            cursor.execute('''
                SELECT reaction_emoji, total_count, likely_users, detection_method, created_at
                FROM smart_reactions
                WHERE message_id = ?
                ORDER BY created_at DESC
            ''', (message_id,))

            reactions = cursor.fetchall()
            conn.close()

            if not reactions:
                await event.respond(f"❌ لم يتم العثور على تفاعلات محفوظة للرسالة رقم {message_id}.")
                return

            response_text = f"👥 **المتفاعلين المحتملين مع الرسالة {message_id}:**\n\n"

            for reaction_emoji, total_count, likely_users_json, detection_method, created_at in reactions:
                response_text += f"{reaction_emoji} **{reaction_emoji}** - العدد: {total_count}\n"
                response_text += f"🕐 وقت الاكتشاف: {created_at}\n"

                try:
                    likely_users = json.loads(likely_users_json)
                    if likely_users:
                        response_text += "👤 **المتفاعلين المحتملين:**\n"
                        for i, user in enumerate(likely_users[:8], 1):
                            name = user['name'] or "مستخدم"
                            username = f"@{user['username']}" if user['username'] else "بدون معرف"
                            method = user.get('method', 'Unknown')
                            response_text += f"   {i}. {name} {username} ({method})\n"

                        if len(likely_users) > 8:
                            response_text += f"   ... و {len(likely_users) - 8} مستخدمين آخرين\n"
                    else:
                        response_text += "   ❌ لا توجد تخمينات متاحة\n"

                except:
                    response_text += "   ❌ خطأ في قراءة بيانات المستخدمين\n"

                response_text += "\n"

            response_text += "💡 **ملاحظة:** هذه تخمينات ذكية بناءً على نشاط المستخدمين وليست بيانات مؤكدة."

            await event.respond(response_text)

        except ValueError:
            await event.respond("❌ معرف الرسالة يجب أن يكون رقم صحيح.")
        except Exception as e:
            await event.respond(f"❌ خطأ: {str(e)}")

    async def handle_stats(self, event):
        """معالجة أمر الإحصائيات"""
        if not self.is_admin(event.sender_id):
            return

        try:
            conn = sqlite3.connect('final_working.db')
            cursor = conn.cursor()

            # إحصائيات المستخدمين
            cursor.execute("SELECT COUNT(*) FROM users")
            total_users = cursor.fetchone()[0]

            # إحصائيات التفاعلات
            cursor.execute("SELECT COUNT(*) FROM smart_reactions")
            total_reactions = cursor.fetchone()[0]

            # أكثر الإيموجي
            cursor.execute("""
                SELECT reaction_emoji, SUM(total_count) as total
                FROM smart_reactions
                GROUP BY reaction_emoji
                ORDER BY total DESC
                LIMIT 5
            """)
            top_emojis = cursor.fetchall()

            # آخر التفاعلات
            cursor.execute("""
                SELECT message_id, reaction_emoji, total_count, created_at
                FROM smart_reactions
                ORDER BY created_at DESC
                LIMIT 5
            """)
            recent_reactions = cursor.fetchall()

            conn.close()

            stats_text = f"""
📊 **إحصائيات البوت الذكي:**

🔄 **حالة المراقبة:** {"🟢 نشط" if self.is_monitoring else "🔴 متوقف"}
📺 **القناة المراقبة:** {getattr(self.monitoring_channel, 'title', 'لا توجد') if self.monitoring_channel else 'لا توجد'}

👥 **المستخدمين:**
• المكتشفين في قاعدة البيانات: {total_users}
• في الكاش النشط: {len(self.users_cache)}
• في القناة المراقبة: {len(self.channel_participants)}

🎭 **التفاعلات:**
• إجمالي التفاعلات المكتشفة: {total_reactions}

🏆 **أكثر الإيموجي:**
"""

            for i, (emoji, count) in enumerate(top_emojis, 1):
                stats_text += f"{i}. {emoji} - {count} مرة\n"

            if recent_reactions:
                stats_text += "\n🕐 **آخر التفاعلات:**\n"
                for msg_id, emoji, count, created_at in recent_reactions:
                    stats_text += f"• {emoji} على الرسالة {msg_id} ({count}) - {created_at}\n"

            stats_text += f"\n📅 **آخر تحديث:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

            await event.respond(stats_text)

        except Exception as e:
            await event.respond(f"❌ خطأ في الحصول على الإحصائيات: {str(e)}")

    async def handle_stop_monitoring(self, event):
        """معالجة أمر إيقاف المراقبة"""
        if not self.is_admin(event.sender_id):
            return

        if not self.is_monitoring:
            await event.respond("⚠️ المراقبة غير مفعلة حالياً.")
            return

        if self.scan_task:
            self.scan_task.cancel()
            self.scan_task = None

        if self.monitor_client:
            await self.monitor_client.disconnect()
            self.monitor_client = None

        self.is_monitoring = False
        self.monitoring_channel = None
        self.last_reactions = {}

        await event.respond("✅ تم إيقاف المراقبة الذكية بنجاح.")

    async def handle_callback(self, event):
        """معالجة أزرار الاستجابة"""
        if not self.is_admin(event.sender_id):
            return

        data = event.data.decode('utf-8')

        if data == "users":
            await self.handle_get_users(event)
        elif data == "stats":
            await self.handle_stats(event)

        await event.answer()

    def is_admin(self, user_id: int) -> bool:
        """التحقق من صلاحيات المدير"""
        return user_id == self.admin_user_id

    async def send_welcome_message(self):
        """إرسال رسالة ترحيب للمدير"""
        try:
            message = """
🤖 **تم تشغيل البوت النهائي الذكي بنجاح!**

✅ **يعمل بنجاح:**
• مسح المستخدمين (200+ مستخدم)
• تخمين ذكي للمتفاعلين
• تنبيهات مع أسماء المستخدمين المحتملين
• إحصائيات مفصلة

استخدم /start للبدء.
            """
            await self.bot.send_message(self.admin_user_id, message)
        except Exception as e:
            print(f"لا يمكن إرسال رسالة الترحيب: {e}")

    async def run(self):
        """تشغيل البوت"""
        await self.start()
        await self.bot.run_until_disconnected()

async def main():
    print("🔄 بدء تشغيل البوت النهائي الذكي...")
    bot = FinalWorkingBot()
    await bot.run()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
