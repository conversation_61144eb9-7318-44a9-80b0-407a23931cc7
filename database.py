import sqlite3
import json
from datetime import datetime
from typing import List, Dict, Optional
import logging

class ReactionsDatabase:
    def __init__(self, db_path: str = "reactions_data.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء جداول قاعدة البيانات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # جدول الرسائل
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS messages (
                        id INTEGER PRIMARY KEY,
                        channel_id INTEGER,
                        message_id INTEGER,
                        message_text TEXT,
                        message_date TIMESTAMP,
                        total_reactions INTEGER DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(channel_id, message_id)
                    )
                ''')
                
                # جدول التفاعلات
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS reactions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        message_table_id INTEGER,
                        user_id INTEGER,
                        user_name TEXT,
                        user_username TEXT,
                        reaction_emoji TEXT,
                        reaction_date TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (message_table_id) REFERENCES messages (id),
                        UNIQUE(message_table_id, user_id, reaction_emoji)
                    )
                ''')
                
                # جدول الإحصائيات اليومية
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS daily_stats (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        date DATE,
                        channel_id INTEGER,
                        total_reactions INTEGER,
                        unique_users INTEGER,
                        most_popular_emoji TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(date, channel_id)
                    )
                ''')
                
                conn.commit()
                logging.info("تم إنشاء قاعدة البيانات بنجاح")
                
        except Exception as e:
            logging.error(f"خطأ في إنشاء قاعدة البيانات: {e}")
    
    def add_message(self, channel_id: int, message_id: int, message_text: str, message_date: datetime) -> int:
        """إضافة رسالة جديدة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO messages 
                    (channel_id, message_id, message_text, message_date)
                    VALUES (?, ?, ?, ?)
                ''', (channel_id, message_id, message_text, message_date))
                
                # الحصول على ID الرسالة في قاعدة البيانات
                cursor.execute('''
                    SELECT id FROM messages WHERE channel_id = ? AND message_id = ?
                ''', (channel_id, message_id))
                
                result = cursor.fetchone()
                return result[0] if result else None
                
        except Exception as e:
            logging.error(f"خطأ في إضافة الرسالة: {e}")
            return None
    
    def add_reaction(self, message_table_id: int, user_id: int, user_name: str, 
                    user_username: str, reaction_emoji: str, reaction_date: datetime) -> bool:
        """إضافة تفاعل جديد"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT OR REPLACE INTO reactions 
                    (message_table_id, user_id, user_name, user_username, reaction_emoji, reaction_date)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (message_table_id, user_id, user_name, user_username, reaction_emoji, reaction_date))
                
                # تحديث عدد التفاعلات في جدول الرسائل
                cursor.execute('''
                    UPDATE messages 
                    SET total_reactions = (
                        SELECT COUNT(*) FROM reactions WHERE message_table_id = ?
                    )
                    WHERE id = ?
                ''', (message_table_id, message_table_id))
                
                conn.commit()
                return True
                
        except Exception as e:
            logging.error(f"خطأ في إضافة التفاعل: {e}")
            return False
    
    def get_message_reactions(self, channel_id: int, message_id: int) -> List[Dict]:
        """الحصول على تفاعلات رسالة معينة"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT r.user_id, r.user_name, r.user_username, r.reaction_emoji, r.reaction_date
                    FROM reactions r
                    JOIN messages m ON r.message_table_id = m.id
                    WHERE m.channel_id = ? AND m.message_id = ?
                    ORDER BY r.reaction_date DESC
                ''', (channel_id, message_id))
                
                reactions = []
                for row in cursor.fetchall():
                    reactions.append({
                        'user_id': row[0],
                        'user_name': row[1],
                        'user_username': row[2],
                        'reaction_emoji': row[3],
                        'reaction_date': row[4]
                    })
                
                return reactions
                
        except Exception as e:
            logging.error(f"خطأ في الحصول على التفاعلات: {e}")
            return []
    
    def get_top_reactors(self, channel_id: int, limit: int = 10) -> List[Dict]:
        """الحصول على أكثر المتفاعلين"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT r.user_id, r.user_name, r.user_username, COUNT(*) as reaction_count
                    FROM reactions r
                    JOIN messages m ON r.message_table_id = m.id
                    WHERE m.channel_id = ?
                    GROUP BY r.user_id, r.user_name, r.user_username
                    ORDER BY reaction_count DESC
                    LIMIT ?
                ''', (channel_id, limit))
                
                top_reactors = []
                for row in cursor.fetchall():
                    top_reactors.append({
                        'user_id': row[0],
                        'user_name': row[1],
                        'user_username': row[2],
                        'reaction_count': row[3]
                    })
                
                return top_reactors
                
        except Exception as e:
            logging.error(f"خطأ في الحصول على أكثر المتفاعلين: {e}")
            return []
    
    def get_popular_emojis(self, channel_id: int, limit: int = 10) -> List[Dict]:
        """الحصول على أكثر الإيموجي استخداماً"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    SELECT r.reaction_emoji, COUNT(*) as usage_count
                    FROM reactions r
                    JOIN messages m ON r.message_table_id = m.id
                    WHERE m.channel_id = ?
                    GROUP BY r.reaction_emoji
                    ORDER BY usage_count DESC
                    LIMIT ?
                ''', (channel_id, limit))
                
                popular_emojis = []
                for row in cursor.fetchall():
                    popular_emojis.append({
                        'emoji': row[0],
                        'count': row[1]
                    })
                
                return popular_emojis
                
        except Exception as e:
            logging.error(f"خطأ في الحصول على الإيموجي الشائعة: {e}")
            return []
