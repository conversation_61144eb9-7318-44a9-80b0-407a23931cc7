#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import logging
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, List
import json

from dotenv import load_dotenv
from telethon import TelegramClient, events, Button
from telethon.tl.types import User

from telegram_monitor import TelegramReactionMonitor
from database import ReactionsDatabase

# تحميل متغيرات البيئة
load_dotenv()

class ReactionBot:
    def __init__(self):
        self.api_id = int(os.getenv('API_ID'))
        self.api_hash = os.getenv('API_HASH')
        self.bot_token = os.getenv('BOT_TOKEN')
        self.admin_user_id = int(os.getenv('ADMIN_USER_ID', 0))
        
        # إعداد البوت
        self.bot = TelegramClient('reaction_bot', self.api_id, self.api_hash)
        
        # مراقب التفاعلات
        self.monitor = TelegramReactionMonitor()
        
        # قاعدة البيانات
        self.db = ReactionsDatabase()
        
        # إعداد السجلات
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('reaction_bot.log', encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
        
        # حالة البوت
        self.is_monitoring = False
        self.monitor_task = None
    
    async def start(self):
        """بدء تشغيل البوت"""
        try:
            await self.bot.start(bot_token=self.bot_token)
            self.logger.info("تم تشغيل البوت بنجاح")
            
            # تسجيل معالجات الأوامر
            self.register_handlers()
            
            # إرسال رسالة ترحيب للمدير
            if self.admin_user_id:
                await self.send_welcome_message()
            
            self.logger.info("البوت جاهز للاستخدام!")
            
        except Exception as e:
            self.logger.error(f"خطأ في تشغيل البوت: {e}")
    
    def register_handlers(self):
        """تسجيل معالجات الأوامر"""
        
        @self.bot.on(events.NewMessage(pattern='/start'))
        async def start_command(event):
            await self.handle_start(event)
        
        @self.bot.on(events.NewMessage(pattern='/help'))
        async def help_command(event):
            await self.handle_help(event)
        
        @self.bot.on(events.NewMessage(pattern='/monitor'))
        async def monitor_command(event):
            await self.handle_monitor(event)
        
        @self.bot.on(events.NewMessage(pattern='/stop'))
        async def stop_command(event):
            await self.handle_stop_monitoring(event)
        
        @self.bot.on(events.NewMessage(pattern='/stats'))
        async def stats_command(event):
            await self.handle_stats(event)
        
        @self.bot.on(events.NewMessage(pattern='/top'))
        async def top_command(event):
            await self.handle_top_reactors(event)
        
        @self.bot.on(events.NewMessage(pattern='/emojis'))
        async def emojis_command(event):
            await self.handle_popular_emojis(event)
        
        @self.bot.on(events.NewMessage(pattern='/export'))
        async def export_command(event):
            await self.handle_export_data(event)
        
        @self.bot.on(events.CallbackQuery)
        async def callback_handler(event):
            await self.handle_callback(event)
    
    async def handle_start(self, event):
        """معالجة أمر البدء"""
        if not self.is_admin(event.sender_id):
            await event.respond("❌ عذراً، هذا البوت مخصص للمدير فقط.")
            return
        
        welcome_text = """
🤖 **مرحباً بك في بوت مراقبة التفاعلات!**

هذا البوت يساعدك في مراقبة وتحليل تفاعلات قنوات التليجرام.

**الأوامر المتاحة:**
/help - عرض المساعدة
/monitor - بدء مراقبة قناة
/stop - إيقاف المراقبة
/stats - عرض الإحصائيات العامة
/top - أكثر المتفاعلين
/emojis - الإيموجي الأكثر استخداماً
/export - تصدير البيانات

استخدم /help للحصول على تفاصيل أكثر.
        """
        
        buttons = [
            [Button.inline("📊 الإحصائيات", b"stats")],
            [Button.inline("🔍 بدء المراقبة", b"start_monitor")],
            [Button.inline("❓ المساعدة", b"help")]
        ]
        
        await event.respond(welcome_text, buttons=buttons)
    
    async def handle_help(self, event):
        """معالجة أمر المساعدة"""
        if not self.is_admin(event.sender_id):
            return
        
        help_text = """
📖 **دليل استخدام البوت:**

**🔍 المراقبة:**
• `/monitor @channel_name` - بدء مراقبة قناة معينة
• `/stop` - إيقاف المراقبة الحالية

**📊 الإحصائيات:**
• `/stats` - إحصائيات عامة
• `/top [عدد]` - أكثر المتفاعلين (افتراضي: 10)
• `/emojis [عدد]` - الإيموجي الأكثر استخداماً

**📁 البيانات:**
• `/export` - تصدير البيانات إلى ملف JSON

**💡 نصائح:**
• تأكد من أن البوت عضو في القناة المراد مراقبتها
• البوت يحفظ جميع التفاعلات في قاعدة بيانات محلية
• يمكن مراقبة قناة واحدة في كل مرة
        """
        
        await event.respond(help_text)
    
    async def handle_monitor(self, event):
        """معالجة أمر بدء المراقبة"""
        if not self.is_admin(event.sender_id):
            return
        
        # استخراج اسم القناة من الرسالة
        parts = event.message.message.split()
        if len(parts) < 2:
            await event.respond("❌ يرجى تحديد القناة: `/monitor @channel_name`")
            return
        
        channel_name = parts[1]
        
        if self.is_monitoring:
            await event.respond("⚠️ المراقبة قيد التشغيل بالفعل. استخدم /stop لإيقافها أولاً.")
            return
        
        try:
            await event.respond("🔄 جاري بدء المراقبة...")
            
            # بدء مراقبة القناة
            success = await self.start_monitoring(channel_name)
            
            if success:
                await event.respond(f"✅ تم بدء مراقبة القناة: {channel_name}")
            else:
                await event.respond("❌ فشل في بدء المراقبة. تحقق من اسم القناة والصلاحيات.")
                
        except Exception as e:
            await event.respond(f"❌ خطأ: {str(e)}")
            self.logger.error(f"خطأ في بدء المراقبة: {e}")
    
    async def start_monitoring(self, channel_name: str) -> bool:
        """بدء مراقبة قناة"""
        try:
            # تحديث متغير البيئة
            os.environ['TARGET_CHANNEL'] = channel_name
            
            # إنشاء مراقب جديد
            self.monitor = TelegramReactionMonitor()
            
            # بدء المراقبة في مهمة منفصلة
            self.monitor_task = asyncio.create_task(self.monitor.start())
            self.is_monitoring = True
            
            return True
            
        except Exception as e:
            self.logger.error(f"خطأ في بدء المراقبة: {e}")
            return False
    
    async def handle_stop_monitoring(self, event):
        """معالجة أمر إيقاف المراقبة"""
        if not self.is_admin(event.sender_id):
            return
        
        if not self.is_monitoring:
            await event.respond("⚠️ المراقبة غير مفعلة حالياً.")
            return
        
        try:
            # إيقاف المراقبة
            if self.monitor_task:
                self.monitor_task.cancel()
            
            await self.monitor.stop()
            self.is_monitoring = False
            
            await event.respond("✅ تم إيقاف المراقبة بنجاح.")
            
        except Exception as e:
            await event.respond(f"❌ خطأ في إيقاف المراقبة: {str(e)}")
    
    async def handle_stats(self, event):
        """معالجة أمر الإحصائيات"""
        if not self.is_admin(event.sender_id):
            return

        try:
            # الحصول على إحصائيات من قاعدة البيانات
            # هنا يمكن إضافة استعلامات مخصصة للإحصائيات

            stats_text = """
📊 **إحصائيات عامة:**

🔄 حالة المراقبة: {}
📅 آخر تحديث: {}
💾 حجم قاعدة البيانات: متاح

استخدم الأزرار أدناه للحصول على تفاصيل أكثر.
            """.format(
                "🟢 نشط" if self.is_monitoring else "🔴 متوقف",
                datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            )

            buttons = [
                [Button.inline("👥 أكثر المتفاعلين", b"top_reactors")],
                [Button.inline("😀 الإيموجي الشائعة", b"popular_emojis")],
                [Button.inline("📁 تصدير البيانات", b"export_data")]
            ]

            await event.respond(stats_text, buttons=buttons)

        except Exception as e:
            await event.respond(f"❌ خطأ في الحصول على الإحصائيات: {str(e)}")

    async def handle_top_reactors(self, event):
        """معالجة أمر أكثر المتفاعلين"""
        if not self.is_admin(event.sender_id):
            return

        try:
            # استخراج العدد من الرسالة
            parts = event.message.message.split()
            limit = 10
            if len(parts) > 1 and parts[1].isdigit():
                limit = min(int(parts[1]), 50)  # حد أقصى 50

            # الحصول على أكثر المتفاعلين من جميع القنوات
            # سنستخدم قيمة افتراضية للاختبار
            channel_id = 0

            top_reactors = self.db.get_top_reactors(channel_id, limit)

            if not top_reactors:
                await event.respond("📭 لا توجد بيانات تفاعلات حتى الآن.")
                return

            text = f"👥 **أكثر {len(top_reactors)} متفاعلين:**\n\n"

            for i, reactor in enumerate(top_reactors, 1):
                name = reactor['user_name'] or "مستخدم مجهول"
                username = f"@{reactor['user_username']}" if reactor['user_username'] else ""
                count = reactor['reaction_count']

                text += f"{i}. {name} {username}\n   💬 {count} تفاعل\n\n"

            await event.respond(text)

        except Exception as e:
            await event.respond(f"❌ خطأ في الحصول على أكثر المتفاعلين: {str(e)}")

    async def handle_popular_emojis(self, event):
        """معالجة أمر الإيموجي الشائعة"""
        if not self.is_admin(event.sender_id):
            return

        try:
            # استخراج العدد من الرسالة
            parts = event.message.message.split()
            limit = 10
            if len(parts) > 1 and parts[1].isdigit():
                limit = min(int(parts[1]), 20)

            channel_id = 0  # يجب تحديد هذا
            popular_emojis = self.db.get_popular_emojis(channel_id, limit)

            if not popular_emojis:
                await event.respond("📭 لا توجد بيانات إيموجي حتى الآن.")
                return

            text = f"😀 **أكثر {len(popular_emojis)} إيموجي استخداماً:**\n\n"

            for i, emoji_data in enumerate(popular_emojis, 1):
                emoji = emoji_data['emoji']
                count = emoji_data['count']
                text += f"{i}. {emoji} - {count} مرة\n"

            await event.respond(text)

        except Exception as e:
            await event.respond(f"❌ خطأ في الحصول على الإيموجي الشائعة: {str(e)}")

    async def handle_export_data(self, event):
        """معالجة أمر تصدير البيانات"""
        if not self.is_admin(event.sender_id):
            return

        try:
            await event.respond("🔄 جاري تصدير البيانات...")

            # إنشاء ملف JSON مع البيانات
            export_data = {
                'export_date': datetime.now().isoformat(),
                'monitoring_status': self.is_monitoring,
                'data': {
                    'top_reactors': self.db.get_top_reactors(0, 100),
                    'popular_emojis': self.db.get_popular_emojis(0, 50)
                }
            }

            filename = f"reactions_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)

            # إرسال الملف
            await event.respond("📁 تم تصدير البيانات:", file=filename)

            # حذف الملف المؤقت
            os.remove(filename)

        except Exception as e:
            await event.respond(f"❌ خطأ في تصدير البيانات: {str(e)}")

    async def handle_callback(self, event):
        """معالجة أزرار الاستجابة"""
        if not self.is_admin(event.sender_id):
            return

        data = event.data.decode('utf-8')

        if data == "stats":
            await self.handle_stats(event)
        elif data == "start_monitor":
            await event.respond("استخدم الأمر: `/monitor @channel_name`")
        elif data == "help":
            await self.handle_help(event)
        elif data == "top_reactors":
            await self.handle_top_reactors(event)
        elif data == "popular_emojis":
            await self.handle_popular_emojis(event)
        elif data == "export_data":
            await self.handle_export_data(event)

        await event.answer()
    
    def is_admin(self, user_id: int) -> bool:
        """التحقق من صلاحيات المدير"""
        return user_id == self.admin_user_id
    
    async def send_welcome_message(self):
        """إرسال رسالة ترحيب للمدير"""
        try:
            message = "🤖 تم تشغيل بوت مراقبة التفاعلات بنجاح!\n\nاستخدم /start للبدء."
            await self.bot.send_message(self.admin_user_id, message)
        except Exception as e:
            self.logger.error(f"لا يمكن إرسال رسالة الترحيب: {e}")
    
    async def run(self):
        """تشغيل البوت"""
        await self.start()
        await self.bot.run_until_disconnected()

# دالة التشغيل الرئيسية
async def main():
    bot = ReactionBot()
    await bot.run()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
