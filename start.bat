@echo off
chcp 65001 >nul
title بوت مراقبة تفاعلات التليجرام

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                                                              ║
echo ║    🤖 بوت مراقبة تفاعلات التليجرام                          ║
echo ║                                                              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

REM التحقق من وجود Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo 💡 قم بتثبيت Python من: https://python.org
    pause
    exit /b 1
)

REM التحقق من وجود ملف .env
if not exist ".env" (
    echo ⚠️ ملف .env غير موجود
    echo 🛠️ تشغيل معالج الإعداد...
    echo.
    python setup.py
    echo.
    if not exist ".env" (
        echo ❌ فشل في إنشاء ملف .env
        pause
        exit /b 1
    )
)

REM تثبيت المتطلبات إذا لم تكن مثبتة
echo 📦 التحقق من المتطلبات...
pip show telethon >nul 2>&1
if errorlevel 1 (
    echo 🔄 تثبيت المتطلبات...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المتطلبات
        pause
        exit /b 1
    )
)

echo ✅ جميع المتطلبات مثبتة
echo.
echo 🚀 تشغيل البوت...
echo.

REM تشغيل البوت
python run.py

echo.
echo 🛑 تم إيقاف البوت
pause
