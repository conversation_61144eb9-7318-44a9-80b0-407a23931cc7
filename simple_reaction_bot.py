#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🤖 بوت مراقبة التفاعلات - نسخة مبسطة
"""

import asyncio
import os
import sqlite3
from datetime import datetime
from dotenv import load_dotenv
from telethon import TelegramClient, events, Button

# تحميل متغيرات البيئة
load_dotenv()

class SimpleReactionBot:
    def __init__(self):
        self.api_id = int(os.getenv('API_ID'))
        self.api_hash = os.getenv('API_HASH')
        self.bot_token = os.getenv('BOT_TOKEN')
        self.phone_number = os.getenv('PHONE_NUMBER')
        self.admin_user_id = int(os.getenv('ADMIN_USER_ID'))
        
        # إعداد البوت
        self.bot = TelegramClient('simple_reaction_bot', self.api_id, self.api_hash)
        
        # إعداد قاعدة البيانات البسيطة
        self.init_database()
        
        # متغيرات المراقبة
        self.monitoring_channel = None
        self.is_monitoring = False
        self.monitor_client = None
    
    def init_database(self):
        """إنشاء قاعدة بيانات بسيطة"""
        try:
            conn = sqlite3.connect('reactions.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS reactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_name TEXT,
                    message_id INTEGER,
                    user_name TEXT,
                    user_id INTEGER,
                    reaction_emoji TEXT,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة البيانات")
            
        except Exception as e:
            print(f"❌ خطأ في قاعدة البيانات: {e}")
    
    async def start(self):
        """بدء تشغيل البوت"""
        try:
            await self.bot.start(bot_token=self.bot_token)
            print("✅ تم تشغيل البوت بنجاح!")
            
            # تسجيل معالجات الأوامر
            self.register_handlers()
            
            # إرسال رسالة ترحيب
            if self.admin_user_id:
                await self.send_welcome_message()
            
            print("🤖 البوت جاهز للاستخدام!")
            
        except Exception as e:
            print(f"❌ خطأ في تشغيل البوت: {e}")
    
    def register_handlers(self):
        """تسجيل معالجات الأوامر"""
        
        @self.bot.on(events.NewMessage(pattern='/start'))
        async def start_command(event):
            await self.handle_start(event)
        
        @self.bot.on(events.NewMessage(pattern='/help'))
        async def help_command(event):
            await self.handle_help(event)
        
        @self.bot.on(events.NewMessage(pattern='/monitor'))
        async def monitor_command(event):
            await self.handle_monitor(event)
        
        @self.bot.on(events.NewMessage(pattern='/stop'))
        async def stop_command(event):
            await self.handle_stop_monitoring(event)
        
        @self.bot.on(events.NewMessage(pattern='/stats'))
        async def stats_command(event):
            await self.handle_stats(event)
        
        @self.bot.on(events.CallbackQuery)
        async def callback_handler(event):
            await self.handle_callback(event)
    
    async def handle_start(self, event):
        """معالجة أمر البدء"""
        if not self.is_admin(event.sender_id):
            await event.respond("❌ عذراً، هذا البوت مخصص للمدير فقط.")
            return
        
        welcome_text = """
🤖 **مرحباً بك في بوت مراقبة التفاعلات!**

هذا البوت يساعدك في مراقبة وتحليل تفاعلات قنوات التليجرام.

**الأوامر المتاحة:**
/help - عرض المساعدة
/monitor @channel - بدء مراقبة قناة
/stop - إيقاف المراقبة
/stats - عرض الإحصائيات

**ملاحظة**: تأكد من أن البوت عضو في القناة المراد مراقبتها.
        """
        
        buttons = [
            [Button.inline("📊 الإحصائيات", b"stats")],
            [Button.inline("❓ المساعدة", b"help")]
        ]
        
        await event.respond(welcome_text, buttons=buttons)
    
    async def handle_help(self, event):
        """معالجة أمر المساعدة"""
        if not self.is_admin(event.sender_id):
            return
        
        help_text = """
📖 **دليل استخدام البوت:**

**🔍 المراقبة:**
• `/monitor @channel_name` - بدء مراقبة قناة معينة
• `/stop` - إيقاف المراقبة الحالية

**📊 الإحصائيات:**
• `/stats` - إحصائيات عامة

**💡 نصائح:**
• تأكد من أن البوت عضو في القناة المراد مراقبتها
• البوت يحفظ جميع التفاعلات في قاعدة بيانات محلية
• يمكن مراقبة قناة واحدة في كل مرة

**⚠️ هذه نسخة مبسطة للاختبار**
        """
        
        await event.respond(help_text)
    
    async def handle_monitor(self, event):
        """معالجة أمر بدء المراقبة"""
        if not self.is_admin(event.sender_id):
            return
        
        # استخراج اسم القناة من الرسالة
        parts = event.message.message.split()
        if len(parts) < 2:
            await event.respond("❌ يرجى تحديد القناة: `/monitor @channel_name`")
            return
        
        channel_name = parts[1]
        
        if self.is_monitoring:
            await event.respond("⚠️ المراقبة قيد التشغيل بالفعل. استخدم /stop لإيقافها أولاً.")
            return
        
        try:
            await event.respond("🔄 جاري بدء المراقبة...")

            # محاولة الوصول للقناة
            entity = await self.bot.get_entity(channel_name)
            self.monitoring_channel = entity

            # بدء مراقبة التفاعلات الفعلية
            await self.start_reaction_monitoring()

            self.is_monitoring = True

            await event.respond(f"✅ تم بدء مراقبة القناة: {entity.title}\n\n🔍 البوت الآن يراقب التفاعلات على الرسائل الجديدة!\n\n💡 جرب إضافة تفاعل على أي رسالة في القناة وستحصل على تنبيه.")

        except Exception as e:
            await event.respond(f"❌ فشل في بدء المراقبة: {str(e)}\n\n💡 تأكد من:\n- صحة اسم القناة\n- أن البوت عضو في القناة\n- أن القناة تسمح بالتفاعلات")
    
    async def handle_stop_monitoring(self, event):
        """معالجة أمر إيقاف المراقبة"""
        if not self.is_admin(event.sender_id):
            return

        if not self.is_monitoring:
            await event.respond("⚠️ المراقبة غير مفعلة حالياً.")
            return

        # إيقاف عميل المراقبة
        if self.monitor_client:
            await self.monitor_client.disconnect()
            self.monitor_client = None

        self.is_monitoring = False
        self.monitoring_channel = None

        await event.respond("✅ تم إيقاف المراقبة بنجاح.")
    
    async def handle_stats(self, event):
        """معالجة أمر الإحصائيات"""
        if not self.is_admin(event.sender_id):
            return
        
        try:
            # إحصائيات بسيطة من قاعدة البيانات
            conn = sqlite3.connect('reactions.db')
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM reactions")
            total_reactions = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT user_id) FROM reactions")
            unique_users = cursor.fetchone()[0]
            
            conn.close()
            
            stats_text = f"""
📊 **إحصائيات البوت:**

🔄 حالة المراقبة: {"🟢 نشط" if self.is_monitoring else "🔴 متوقف"}
📺 القناة المراقبة: {getattr(self.monitoring_channel, 'title', 'لا توجد') if self.monitoring_channel else 'لا توجد'}
💬 إجمالي التفاعلات: {total_reactions}
👥 المستخدمين المتفاعلين: {unique_users}
📅 آخر تحديث: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

⚠️ هذه نسخة مبسطة للاختبار
            """
            
            await event.respond(stats_text)
            
        except Exception as e:
            await event.respond(f"❌ خطأ في الحصول على الإحصائيات: {str(e)}")
    
    async def handle_callback(self, event):
        """معالجة أزرار الاستجابة"""
        if not self.is_admin(event.sender_id):
            return
        
        data = event.data.decode('utf-8')
        
        if data == "stats":
            await self.handle_stats(event)
        elif data == "help":
            await self.handle_help(event)
        
        await event.answer()
    
    def is_admin(self, user_id: int) -> bool:
        """التحقق من صلاحيات المدير"""
        return user_id == self.admin_user_id
    
    async def start_reaction_monitoring(self):
        """بدء مراقبة التفاعلات الفعلية"""
        try:
            # إنشاء عميل منفصل للمراقبة
            self.monitor_client = TelegramClient('monitor_session', self.api_id, self.api_hash)
            await self.monitor_client.start(phone=self.phone_number)

            # تسجيل معالج للرسائل المحدثة (تتضمن التفاعلات)
            @self.monitor_client.on(events.MessageEdited)
            async def handle_message_update(event):
                if event.chat_id == self.monitoring_channel.id:
                    await self.check_reactions(event.message)

            # تسجيل معالج للرسائل الجديدة
            @self.monitor_client.on(events.NewMessage)
            async def handle_new_message(event):
                if event.chat_id == self.monitoring_channel.id:
                    # حفظ الرسالة الجديدة
                    await self.save_message(event.message)

            print(f"✅ بدأت مراقبة التفاعلات للقناة: {self.monitoring_channel.title}")

        except Exception as e:
            print(f"❌ خطأ في بدء مراقبة التفاعلات: {e}")

    async def check_reactions(self, message):
        """فحص تفاعلات الرسالة"""
        try:
            if message.reactions:
                for reaction in message.reactions.results:
                    if hasattr(reaction, 'reaction') and hasattr(reaction.reaction, 'emoticon'):
                        emoji = reaction.reaction.emoticon
                        count = reaction.count

                        # إرسال تنبيه للمدير
                        notification = f"🔔 تفاعل جديد!\n\n📺 القناة: {self.monitoring_channel.title}\n💬 الرسالة: {message.id}\n{emoji} التفاعل: {emoji} ({count})\n⏰ الوقت: {datetime.now().strftime('%H:%M:%S')}"

                        await self.bot.send_message(self.admin_user_id, notification)

                        # حفظ في قاعدة البيانات
                        await self.save_reaction(message.id, emoji, count)

        except Exception as e:
            print(f"خطأ في فحص التفاعلات: {e}")

    async def save_message(self, message):
        """حفظ رسالة جديدة"""
        try:
            conn = sqlite3.connect('reactions.db')
            cursor = conn.cursor()

            cursor.execute('''
                INSERT OR REPLACE INTO reactions
                (channel_name, message_id, user_name, user_id, reaction_emoji, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (self.monitoring_channel.title, message.id, 'نظام', 0, 'رسالة_جديدة', datetime.now()))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"خطأ في حفظ الرسالة: {e}")

    async def save_reaction(self, message_id, emoji, count):
        """حفظ تفاعل في قاعدة البيانات"""
        try:
            conn = sqlite3.connect('reactions.db')
            cursor = conn.cursor()

            cursor.execute('''
                INSERT INTO reactions
                (channel_name, message_id, user_name, user_id, reaction_emoji, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (self.monitoring_channel.title, message_id, f'تفاعل_{count}', 0, emoji, datetime.now()))

            conn.commit()
            conn.close()

        except Exception as e:
            print(f"خطأ في حفظ التفاعل: {e}")

    async def send_welcome_message(self):
        """إرسال رسالة ترحيب للمدير"""
        try:
            message = "🤖 تم تشغيل بوت مراقبة التفاعلات بنجاح!\n\nاستخدم /start للبدء.\n\n🔍 البوت الآن يدعم مراقبة التفاعلات الفعلية!"
            await self.bot.send_message(self.admin_user_id, message)
        except Exception as e:
            print(f"لا يمكن إرسال رسالة الترحيب: {e}")
    
    async def run(self):
        """تشغيل البوت"""
        await self.start()
        await self.bot.run_until_disconnected()

async def main():
    print("🔄 بدء تشغيل بوت مراقبة التفاعلات...")
    bot = SimpleReactionBot()
    await bot.run()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
