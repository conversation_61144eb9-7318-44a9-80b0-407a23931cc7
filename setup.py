#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🛠️ معالج الإعداد السريع لبوت مراقبة التفاعلات
يساعد في إعداد البوت بسهولة
"""

import os
import sys
from pathlib import Path

def print_banner():
    """طباعة شعار الإعداد"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    🛠️ معالج الإعداد السريع                                  ║
║    بوت مراقبة تفاعلات التليجرام                            ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def get_user_input(prompt, required=True, default=None):
    """الحصول على مدخلات المستخدم"""
    while True:
        if default:
            user_input = input(f"{prompt} [{default}]: ").strip()
            if not user_input:
                return default
        else:
            user_input = input(f"{prompt}: ").strip()
        
        if user_input or not required:
            return user_input
        
        print("❌ هذا الحقل مطلوب!")

def validate_api_id(api_id):
    """التحقق من صحة API_ID"""
    try:
        return int(api_id) > 0
    except ValueError:
        return False

def create_env_file():
    """إنشاء ملف .env"""
    print("\n📝 إعداد متغيرات البيئة:")
    print("=" * 50)
    
    # جمع البيانات من المستخدم
    print("\n🔑 بيانات Telegram API:")
    print("احصل عليها من: https://my.telegram.org")
    
    while True:
        api_id = get_user_input("API_ID")
        if validate_api_id(api_id):
            break
        print("❌ API_ID يجب أن يكون رقم صحيح!")
    
    api_hash = get_user_input("API_HASH")
    
    print("\n🤖 بيانات البوت:")
    print("احصل على TOKEN من: @BotFather")
    bot_token = get_user_input("BOT_TOKEN")
    
    print("\n📱 بيانات المستخدم:")
    phone_number = get_user_input("رقم الهاتف (مع رمز البلد)", default="+966")
    
    print("\n👤 معرف المدير:")
    print("احصل عليه من: @userinfobot")
    admin_user_id = get_user_input("ADMIN_USER_ID")
    
    print("\n📺 القناة المراد مراقبتها:")
    target_channel = get_user_input("اسم القناة (مثل: @my_channel)", required=False, default="@my_channel")
    
    # إنشاء محتوى ملف .env
    env_content = f"""# Telegram API Configuration
API_ID={api_id}
API_HASH={api_hash}
BOT_TOKEN={bot_token}
PHONE_NUMBER={phone_number}

# Database Configuration
DATABASE_PATH=reactions_data.db

# Monitoring Configuration
TARGET_CHANNEL={target_channel}
ADMIN_USER_ID={admin_user_id}

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=bot.log
"""
    
    # كتابة الملف
    try:
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        
        print("\n✅ تم إنشاء ملف .env بنجاح!")
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في إنشاء ملف .env: {e}")
        return False

def install_requirements():
    """تثبيت المتطلبات"""
    print("\n📦 تثبيت المتطلبات...")
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم تثبيت المتطلبات بنجاح!")
            return True
        else:
            print(f"❌ خطأ في تثبيت المتطلبات: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تثبيت المتطلبات: {e}")
        return False

def check_files():
    """التحقق من وجود الملفات المطلوبة"""
    required_files = [
        'requirements.txt',
        'reaction_bot.py',
        'telegram_monitor.py',
        'database.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ ملفات مفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    return True

def show_next_steps():
    """عرض الخطوات التالية"""
    print("\n🎉 تم الإعداد بنجاح!")
    print("=" * 50)
    print("\n📋 الخطوات التالية:")
    print("1. تشغيل البوت: python run.py")
    print("2. إرسال /start للبوت في التليجرام")
    print("3. استخدام /monitor @channel_name لبدء المراقبة")
    print("\n💡 نصائح:")
    print("- تأكد من أن البوت عضو في القناة المراد مراقبتها")
    print("- راجع ملف README.md للمزيد من التفاصيل")
    print("- احتفظ بنسخة احتياطية من ملف .env")

def main():
    """الدالة الرئيسية للإعداد"""
    print_banner()
    
    # التحقق من وجود الملفات
    print("🔍 التحقق من الملفات المطلوبة...")
    if not check_files():
        print("\n❌ بعض الملفات مفقودة. تأكد من تحميل جميع ملفات المشروع.")
        return
    
    print("✅ جميع الملفات موجودة!")
    
    # التحقق من وجود ملف .env
    if Path('.env').exists():
        overwrite = input("\n⚠️ ملف .env موجود بالفعل. هل تريد استبداله؟ (y/N): ").strip().lower()
        if overwrite not in ['y', 'yes', 'نعم']:
            print("تم إلغاء الإعداد.")
            return
    
    # إنشاء ملف .env
    if not create_env_file():
        return
    
    # تثبيت المتطلبات
    install_choice = input("\n📦 هل تريد تثبيت المتطلبات الآن؟ (Y/n): ").strip().lower()
    if install_choice not in ['n', 'no', 'لا']:
        install_requirements()
    
    # عرض الخطوات التالية
    show_next_steps()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n🛑 تم إلغاء الإعداد بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في الإعداد: {e}")
