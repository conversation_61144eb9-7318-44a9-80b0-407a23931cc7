#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🤖 بوت مراقبة التفاعلات الصادق
يخبرك الحقيقة حول ما يمكن وما لا يمكن فعله
"""

import asyncio
import os
import sqlite3
from datetime import datetime
from dotenv import load_dotenv
from telethon import TelegramClient, events, Button

# تحميل متغيرات البيئة
load_dotenv()

class HonestReactionBot:
    def __init__(self):
        self.api_id = int(os.getenv('API_ID'))
        self.api_hash = os.getenv('API_HASH')
        self.bot_token = os.getenv('BOT_TOKEN')
        self.phone_number = os.getenv('PHONE_NUMBER')
        self.admin_user_id = int(os.getenv('ADMIN_USER_ID'))
        
        # إعداد البوت
        self.bot = TelegramClient('honest_reaction_bot', self.api_id, self.api_hash)
        
        # إعداد قاعدة البيانات
        self.init_database()
        
        # متغيرات المراقبة
        self.monitoring_channel = None
        self.is_monitoring = False
        self.monitor_client = None
        self.scan_task = None
        self.last_reactions = {}
    
    def init_database(self):
        """إنشاء قاعدة بيانات واقعية"""
        try:
            conn = sqlite3.connect('honest_reactions.db')
            cursor = conn.cursor()
            
            # جدول التفاعلات (بدون أسماء مستخدمين)
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS reactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_name TEXT,
                    message_id INTEGER,
                    message_text TEXT,
                    reaction_emoji TEXT,
                    reaction_count INTEGER,
                    previous_count INTEGER,
                    increase_amount INTEGER,
                    detected_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # جدول إحصائيات القناة
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS channel_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    channel_name TEXT,
                    total_messages_scanned INTEGER,
                    total_reactions_detected INTEGER,
                    most_popular_emoji TEXT,
                    scan_date DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # جدول المشتركين
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS subscribers (
                    user_id INTEGER PRIMARY KEY,
                    first_name TEXT,
                    last_name TEXT,
                    username TEXT,
                    phone TEXT,
                    is_bot INTEGER DEFAULT 0,
                    is_premium INTEGER DEFAULT 0,
                    last_online DATETIME,
                    added_to_db DATETIME DEFAULT CURRENT_TIMESTAMP,
                    channel_name TEXT
                )
            ''')
            
            conn.commit()
            conn.close()
            print("✅ تم إنشاء قاعدة البيانات الواقعية")
            
        except Exception as e:
            print(f"❌ خطأ في قاعدة البيانات: {e}")
    
    async def start(self):
        """بدء تشغيل البوت"""
        try:
            await self.bot.start(bot_token=self.bot_token)
            print("✅ تم تشغيل البوت بنجاح!")
            
            self.register_handlers()
            
            if self.admin_user_id:
                await self.send_welcome_message()
            
            print("🤖 البوت الصادق جاهز للاستخدام!")
            
        except Exception as e:
            print(f"❌ خطأ في تشغيل البوت: {e}")
    
    def register_handlers(self):
        """تسجيل معالجات الأوامر"""
        
        @self.bot.on(events.NewMessage(pattern='/start'))
        async def start_command(event):
            await self.handle_start(event)
        
        @self.bot.on(events.NewMessage(pattern='/monitor'))
        async def monitor_command(event):
            await self.handle_monitor(event)
        
        @self.bot.on(events.NewMessage(pattern='/stats'))
        async def stats_command(event):
            await self.handle_stats(event)
        
        @self.bot.on(events.NewMessage(pattern='/truth'))
        async def truth_command(event):
            await self.handle_truth(event)
        
        @self.bot.on(events.NewMessage(pattern='/alternatives'))
        async def alternatives_command(event):
            await self.handle_alternatives(event)

        @self.bot.on(events.NewMessage(pattern='/subscribers'))
        async def subscribers_command(event):
            await self.handle_subscribers(event)

        @self.bot.on(events.NewMessage(pattern='/scan_users'))
        async def scan_users_command(event):
            await self.handle_scan_users(event)

        @self.bot.on(events.NewMessage(pattern='/admin_help'))
        async def admin_help_command(event):
            await self.handle_admin_help(event)

        @self.bot.on(events.NewMessage(pattern='/stop'))
        async def stop_command(event):
            await self.handle_stop_monitoring(event)
    
    async def handle_start(self, event):
        """معالجة أمر البدء"""
        if not self.is_admin(event.sender_id):
            await event.respond("❌ عذراً، هذا البوت مخصص للمدير فقط.")
            return
        
        welcome_text = """
🤖 **بوت مراقبة التفاعلات الصادق**

**🔍 الحقيقة الواضحة:**

**✅ ما يمكنني فعله:**
• مراقبة التفاعلات (العدد والنوع)
• تتبع زيادة/نقصان التفاعلات
• إحصائيات الإيموجي الأكثر استخداماً
• تحليل نشاط القناة العام
• تنبيهات فورية عند تفاعلات جديدة

**❌ ما لا يمكنني فعله:**
• معرفة أسماء المتفاعلين الحقيقية
• تحديد من تفاعل بالضبط مع رسالة
• الحصول على قائمة المتفاعلين في القنوات العامة

**الأوامر:**
/monitor @channel - بدء المراقبة الواقعية
/stats - إحصائيات التفاعلات
/truth - الحقيقة حول قيود Telegram
/alternatives - حلول بديلة
/subscribers - عرض قائمة المشتركين
/scan_users - مسح شامل للمشتركين
/admin_help - كيفية الحصول على صلاحيات إدارية
/stop - إيقاف المراقبة

**💡 هذا البوت صادق معك ولا يدعي قدرات وهمية!**
        """
        
        buttons = [
            [Button.inline("📊 الإحصائيات", b"stats")],
            [Button.inline("👥 المشتركين", b"subscribers")],
            [Button.inline("🔐 مساعدة إدارية", b"admin_help")],
            [Button.inline("🔍 الحقيقة", b"truth")],
            [Button.inline("💡 حلول بديلة", b"alternatives")]
        ]
        
        await event.respond(welcome_text, buttons=buttons)
    
    async def handle_monitor(self, event):
        """معالجة أمر بدء المراقبة الواقعية"""
        if not self.is_admin(event.sender_id):
            return
        
        parts = event.message.message.split()
        if len(parts) < 2:
            await event.respond("❌ يرجى تحديد القناة: `/monitor @channel_name`")
            return
        
        channel_name = parts[1]
        
        if self.is_monitoring:
            await event.respond("⚠️ المراقبة قيد التشغيل بالفعل. استخدم /stop لإيقافها أولاً.")
            return
        
        try:
            await event.respond("🔄 جاري بدء المراقبة الواقعية...")
            
            # إنشاء عميل المراقبة
            self.monitor_client = TelegramClient('monitor_session', self.api_id, self.api_hash)
            await self.monitor_client.start(phone=self.phone_number)
            
            # الحصول على القناة
            entity = await self.monitor_client.get_entity(channel_name)
            self.monitoring_channel = entity
            
            # بدء المراقبة
            await self.start_honest_monitoring()
            self.is_monitoring = True
            
            await event.respond(f"""
✅ **تم بدء المراقبة الواقعية للقناة: {entity.title}**

**ما سأراقبه:**
🎭 التفاعلات الجديدة (العدد والنوع)
📈 زيادة/نقصان عدد التفاعلات
📊 إحصائيات الإيموجي
⏰ وقت حدوث التفاعلات

**ما لن أستطيع معرفته:**
❌ أسماء المتفاعلين الحقيقية
❌ من تفاعل بالضبط

**💡 هذا واقعي وصادق!**
            """)
            
        except Exception as e:
            await event.respond(f"❌ فشل في بدء المراقبة: {str(e)}")
    
    async def start_honest_monitoring(self):
        """بدء المراقبة الصادقة"""
        async def honest_scan_loop():
            while self.is_monitoring:
                try:
                    await self.honest_reaction_scan()
                    await asyncio.sleep(30)
                except Exception as e:
                    print(f"خطأ في المسح الصادق: {e}")
                    await asyncio.sleep(60)
        
        self.scan_task = asyncio.create_task(honest_scan_loop())
        print("✅ بدأت المراقبة الصادقة للتفاعلات")
    
    async def honest_reaction_scan(self):
        """مسح صادق للتفاعلات"""
        try:
            if not self.monitoring_channel or not self.monitor_client:
                return
            
            messages = []
            async for message in self.monitor_client.iter_messages(self.monitoring_channel, limit=20):
                messages.append(message)
            
            new_reactions_found = False
            
            for message in messages:
                if message.reactions:
                    for reaction in message.reactions.results:
                        if hasattr(reaction, 'reaction') and hasattr(reaction.reaction, 'emoticon'):
                            emoji = reaction.reaction.emoticon
                            count = reaction.count
                            
                            message_key = f"{self.monitoring_channel.id}_{message.id}_{emoji}"
                            old_count = self.last_reactions.get(message_key, 0)
                            
                            if count > old_count:
                                # تفاعل جديد - إرسال تنبيه صادق
                                await self.notify_honest_reaction(message, emoji, count, old_count)
                                
                                # حفظ التفاعل
                                await self.save_honest_reaction(message, emoji, count, old_count)
                                
                                new_reactions_found = True
                                self.last_reactions[message_key] = count
            
            if new_reactions_found:
                print(f"🔍 تم العثور على تفاعلات جديدة - {datetime.now().strftime('%H:%M:%S')}")
            
        except Exception as e:
            print(f"خطأ في المسح الصادق: {e}")
    
    async def notify_honest_reaction(self, message, emoji, new_count, old_count):
        """إرسال تنبيه صادق"""
        try:
            message_text = message.message[:50] + "..." if len(message.message) > 50 else message.message
            increase = new_count - old_count
            
            notification = f"""
🔔 **تفاعل جديد مكتشف!**

{emoji} **التفاعل:** {emoji}
📊 **العدد الجديد:** {new_count}
📈 **الزيادة:** +{increase}
📉 **العدد السابق:** {old_count}

📺 **القناة:** {self.monitoring_channel.title}
💬 **الرسالة:** {message.id}
📝 **النص:** {message_text}

⏰ **الوقت:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

❌ **ملاحظة:** لا يمكن معرفة من تفاعل بالضبط بسبب قيود Telegram للقنوات العامة.
            """
            
            await self.bot.send_message(self.admin_user_id, notification)
            
        except Exception as e:
            print(f"خطأ في إرسال التنبيه الصادق: {e}")
    
    async def save_honest_reaction(self, message, emoji, new_count, old_count):
        """حفظ التفاعل الصادق"""
        try:
            conn = sqlite3.connect('honest_reactions.db')
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT INTO reactions 
                (channel_name, message_id, message_text, reaction_emoji, 
                 reaction_count, previous_count, increase_amount)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                self.monitoring_channel.title,
                message.id,
                message.message or "",
                emoji,
                new_count,
                old_count,
                new_count - old_count
            ))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"خطأ في حفظ التفاعل: {e}")
    
    async def handle_stats(self, event):
        """معالجة أمر الإحصائيات الصادقة"""
        if not self.is_admin(event.sender_id):
            return
        
        try:
            conn = sqlite3.connect('honest_reactions.db')
            cursor = conn.cursor()
            
            # إحصائيات التفاعلات
            cursor.execute("SELECT COUNT(*) FROM reactions")
            total_reactions = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT message_id) FROM reactions")
            unique_messages = cursor.fetchone()[0]
            
            cursor.execute("SELECT SUM(increase_amount) FROM reactions")
            total_increases = cursor.fetchone()[0] or 0
            
            # أكثر الإيموجي
            cursor.execute("""
                SELECT reaction_emoji, SUM(increase_amount) as total_increases
                FROM reactions 
                GROUP BY reaction_emoji 
                ORDER BY total_increases DESC 
                LIMIT 5
            """)
            top_emojis = cursor.fetchall()
            
            # آخر التفاعلات
            cursor.execute("""
                SELECT message_id, reaction_emoji, reaction_count, increase_amount, detected_at 
                FROM reactions 
                ORDER BY detected_at DESC 
                LIMIT 5
            """)
            recent_reactions = cursor.fetchall()
            
            conn.close()
            
            stats_text = f"""
📊 **إحصائيات صادقة للتفاعلات:**

🔄 **حالة المراقبة:** {"🟢 نشط" if self.is_monitoring else "🔴 متوقف"}
📺 **القناة المراقبة:** {getattr(self.monitoring_channel, 'title', 'لا توجد') if self.monitoring_channel else 'لا توجد'}

📈 **الإحصائيات:**
🎭 إجمالي التفاعلات المكتشفة: {total_reactions}
💬 الرسائل التي تم رصد تفاعلات عليها: {unique_messages}
📈 إجمالي الزيادات المكتشفة: {total_increases}

🏆 **أكثر الإيموجي نشاط<|im_start|>:**
"""
            
            for i, (emoji, increases) in enumerate(top_emojis, 1):
                stats_text += f"{i}. {emoji} - زيادة {increases} تفاعل\n"
            
            if recent_reactions:
                stats_text += "\n🕐 **آخر التفاعلات المكتشفة:**\n"
                for msg_id, emoji, count, increase, detected_at in recent_reactions:
                    stats_text += f"• {emoji} على الرسالة {msg_id} (+{increase}) - {detected_at}\n"
            
            stats_text += f"\n📅 **آخر تحديث:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            stats_text += f"\n\n❌ **تذكير:** هذه إحصائيات للتفاعلات فقط، بدون أسماء المتفاعلين."
            
            await event.respond(stats_text)
            
        except Exception as e:
            await event.respond(f"❌ خطأ في الحصول على الإحصائيات: {str(e)}")
    
    async def handle_truth(self, event):
        """معالجة أمر الحقيقة"""
        if not self.is_admin(event.sender_id):
            return
        
        truth_text = """
🔍 **الحقيقة الكاملة حول قيود Telegram:**

**❌ ما لا يمكن فعله في القنوات العامة:**
1. **الحصول على أسماء المتفاعلين** - Telegram يحمي خصوصية المستخدمين
2. **معرفة من تفاعل بالضبط** - هذا متاح فقط في المجموعات الصغيرة
3. **تتبع المستخدمين الفرديين** - قيود API صارمة
4. **الوصول لقوائم المتفاعلين** - محظور في القنوات الكبيرة

**✅ ما يمكن فعله:**
1. **مراقبة عدد التفاعلات** - العدد الإجمالي لكل إيموجي
2. **تتبع التغييرات** - زيادة/نقصان التفاعلات
3. **إحصائيات الإيموجي** - أكثر الإيموجي استخداماً
4. **تحليل النشاط العام** - مستوى التفاعل في القناة

**🤔 لماذا هذه القيود؟**
• **حماية الخصوصية** - Telegram يحمي هوية المستخدمين
• **منع التحرش** - لتجنب استهداف المتفاعلين
• **قيود API** - Telegram يحد من الوصول للبيانات الحساسة

**💡 الخلاصة:**
أي بوت يدعي أنه يمكنه معرفة أسماء المتفاعلين في القنوات العامة إما:
• يكذب عليك
• يعطي تخمينات عشوائية
• لا يفهم قيود Telegram
        """
        
        await event.respond(truth_text)
    
    async def handle_alternatives(self, event):
        """معالجة أمر الحلول البديلة"""
        if not self.is_admin(event.sender_id):
            return
        
        alternatives_text = """
💡 **حلول بديلة واقعية:**

**🔄 للحصول على أسماء المتفاعلين:**

**1. إنشاء مجموعة صغيرة:**
• أنشئ مجموعة (ليس قناة) بأقل من 200 عضو
• في المجموعات الصغيرة يمكن رؤية المتفاعلين
• انقل المحتوى المهم للمجموعة

**2. استخدام استطلاعات الرأي:**
• أنشئ استطلاعات بدلاً من الاعتماد على التفاعلات
• يمكن رؤية من صوت في الاستطلاعات
• أكثر تفاعلية ووضوحاً

**3. طلب التعليقات:**
• اطلب من المتابعين التعليق بدلاً من التفاعل
• يمكن رؤية أسماء المعلقين
• تفاعل أكثر ثراءً

**4. إنشاء بوت تفاعلي:**
• أنشئ بوت يطلب من المستخدمين التفاعل معه
• يمكن تتبع من تفاعل مع البوت
• بيانات أكثر دقة

**5. استخدام روابط تتبع:**
• أنشئ روابط مختلفة لكل منشور
• تتبع من ضغط على كل رابط
• إحصائيات دقيقة للاهتمام

**6. التحليل العام:**
• ركز على الإحصائيات العامة
• تحليل أوقات النشاط
• أنواع المحتوى الأكثر تفاعلاً

**💡 الخلاصة:**
بدلاً من محاولة كسر قيود Telegram، استخدم طرق بديلة تحترم الخصوصية وتعطي نتائج أفضل!
        """
        
        await event.respond(alternatives_text)

    async def handle_subscribers(self, event):
        """معالجة أمر عرض المشتركين"""
        if not self.is_admin(event.sender_id):
            return

        if not self.monitoring_channel:
            await event.respond("⚠️ يجب بدء المراقبة أولاً باستخدام /monitor @channel_name")
            return

        try:
            # عرض المشتركين من قاعدة البيانات
            conn = sqlite3.connect('honest_reactions.db')
            cursor = conn.cursor()

            cursor.execute('''
                SELECT COUNT(*) FROM subscribers
                WHERE channel_name = ?
            ''', (self.monitoring_channel.title,))

            total_count = cursor.fetchone()[0]

            cursor.execute('''
                SELECT user_id, first_name, last_name, username, is_bot, is_premium, last_online
                FROM subscribers
                WHERE channel_name = ?
                ORDER BY added_to_db DESC
                LIMIT 20
            ''', (self.monitoring_channel.title,))

            subscribers = cursor.fetchall()
            conn.close()

            if not subscribers:
                await event.respond("""
📭 **لا توجد بيانات مشتركين محفوظة.**

💡 **لمسح المشتركين:**
استخدم `/scan_users` لمسح وحفظ قائمة المشتركين في القناة.
                """)
                return

            subscribers_text = f"""
👥 **مشتركي القناة: {self.monitoring_channel.title}**

📊 **إجمالي المشتركين المحفوظين:** {total_count}
📋 **عرض أول 20 مشترك:**

"""

            for i, (user_id, first_name, last_name, username, is_bot, is_premium, last_online) in enumerate(subscribers, 1):
                name = f"{first_name or ''} {last_name or ''}".strip() or "بدون اسم"
                username_text = f"@{username}" if username else "بدون معرف"

                # رموز إضافية
                bot_icon = "🤖" if is_bot else "👤"
                premium_icon = "⭐" if is_premium else ""

                subscribers_text += f"{i}. {bot_icon} **{name}** {username_text} {premium_icon}\n"
                subscribers_text += f"   🆔 ID: `{user_id}`\n"
                if last_online:
                    subscribers_text += f"   🕐 آخر ظهور: {last_online}\n"
                subscribers_text += "\n"

            if total_count > 20:
                subscribers_text += f"\n... و {total_count - 20} مشترك آخر\n"

            subscribers_text += f"\n💡 **لمسح المزيد:** استخدم `/scan_users`"

            # تقسيم الرسالة إذا كانت طويلة
            if len(subscribers_text) > 4000:
                parts = [subscribers_text[i:i+4000] for i in range(0, len(subscribers_text), 4000)]
                for part in parts:
                    await event.respond(part)
            else:
                await event.respond(subscribers_text)

        except Exception as e:
            await event.respond(f"❌ خطأ في عرض المشتركين: {str(e)}")

    async def handle_scan_users(self, event):
        """معالجة أمر مسح المستخدمين النشطين"""
        if not self.is_admin(event.sender_id):
            return

        if not self.monitoring_channel or not self.monitor_client:
            await event.respond("⚠️ يجب بدء المراقبة أولاً باستخدام /monitor @channel_name")
            return

        try:
            await event.respond("""
🔍 **جاري مسح المستخدمين النشطين...**

⚠️ **ملاحظة:** بسبب قيود Telegram، لا يمكن الوصول لقائمة المشتركين الكاملة في القنوات العامة بدون صلاحيات إدارية.

🔄 **سأحاول الطرق البديلة المتاحة:**
            """)

            scanned_count = 0
            new_count = 0

            # الطريقة الأولى: محاولة المشاركين (قد تفشل)
            try:
                await event.respond("1️⃣ محاولة الوصول للمشاركين المباشرين...")
                participants = await self.monitor_client.get_participants(
                    self.monitoring_channel,
                    limit=200
                )

                for user in participants:
                    result = await self.save_subscriber(user)
                    scanned_count += 1
                    if result:
                        new_count += 1

                await event.respond(f"✅ نجحت الطريقة الأولى! تم مسح {scanned_count} مستخدم.")

            except Exception as e:
                await event.respond(f"❌ الطريقة الأولى فشلت: {str(e)[:100]}...")

                # الطريقة الثانية: من الرسائل الأخيرة
                try:
                    await event.respond("2️⃣ جاري مسح المستخدمين من الرسائل الأخيرة...")

                    users_from_messages = set()
                    message_count = 0

                    async for message in self.monitor_client.iter_messages(self.monitoring_channel, limit=500):
                        message_count += 1
                        if message.from_id and hasattr(message.from_id, 'user_id'):
                            users_from_messages.add(message.from_id.user_id)

                    await event.respond(f"📊 تم فحص {message_count} رسالة، وجدت {len(users_from_messages)} مستخدم نشط.")

                    for user_id in users_from_messages:
                        try:
                            user = await self.monitor_client.get_entity(user_id)
                            result = await self.save_subscriber(user)
                            scanned_count += 1
                            if result:
                                new_count += 1
                        except:
                            continue

                    await event.respond(f"""
✅ **تم مسح المستخدمين النشطين من الرسائل!**

👥 **تم مسح:** {scanned_count} مستخدم نشط
🆕 **جديد:** {new_count} مستخدم
📊 **استخدم /subscribers** لرؤية القائمة

⚠️ **ملاحظة:** هؤلاء المستخدمين النشطين الذين أرسلوا رسائل مؤخراً، وليس جميع المشتركين.
                    """)

                except Exception as e2:
                    await event.respond(f"❌ الطريقة الثانية فشلت أيضاً: {str(e2)[:100]}...")

                    # الطريقة الثالثة: من التفاعلات
                    try:
                        await event.respond("3️⃣ جاري محاولة الحصول على المستخدمين من التفاعلات...")

                        users_from_reactions = set()

                        async for message in self.monitor_client.iter_messages(self.monitoring_channel, limit=100):
                            if message.reactions:
                                # محاولة الحصول على المتفاعلين (قد تفشل)
                                try:
                                    for reaction in message.reactions.results:
                                        if hasattr(reaction, 'reaction'):
                                            # هنا يمكن محاولة الحصول على المتفاعلين
                                            # لكن هذا غالباً سيفشل أيضاً في القنوات العامة
                                            pass
                                except:
                                    continue

                        if not users_from_reactions:
                            await event.respond("""
❌ **فشلت جميع الطرق المتاحة.**

🔒 **السبب:** القناة تتطلب صلاحيات إدارية للوصول لقائمة المشتركين.

💡 **الحلول البديلة:**
1. **اطلب من مدير القناة** إضافة البوت كمدير
2. **استخدم مجموعة بدلاً من قناة** (المجموعات أكثر انفتاحاً)
3. **ركز على مراقبة التفاعلات** بدلاً من قوائم المستخدمين

✅ **ما يعمل حالياً:**
• مراقبة التفاعلات الجديدة
• إحصائيات التفاعلات
• تتبع نشاط القناة العام
                            """)

                    except Exception as e3:
                        await event.respond(f"❌ الطريقة الثالثة فشلت: {str(e3)[:100]}...")

        except Exception as e:
            await event.respond(f"❌ خطأ عام في مسح المستخدمين: {str(e)}")

    async def save_subscriber(self, user):
        """حفظ مشترك في قاعدة البيانات"""
        try:
            conn = sqlite3.connect('honest_reactions.db')
            cursor = conn.cursor()

            # التحقق من وجود المستخدم
            cursor.execute('''
                SELECT user_id FROM subscribers
                WHERE user_id = ? AND channel_name = ?
            ''', (user.id, self.monitoring_channel.title))

            exists = cursor.fetchone()

            # حفظ أو تحديث المستخدم
            cursor.execute('''
                INSERT OR REPLACE INTO subscribers
                (user_id, first_name, last_name, username, phone, is_bot, is_premium,
                 last_online, channel_name)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                user.id,
                user.first_name or '',
                user.last_name or '',
                user.username or '',
                getattr(user, 'phone', '') or '',
                user.bot,
                getattr(user, 'premium', False),
                getattr(user, 'status', None),
                self.monitoring_channel.title
            ))

            conn.commit()
            conn.close()

            # إرجاع True إذا كان مستخدم جديد
            return not exists

        except Exception as e:
            print(f"خطأ في حفظ المشترك: {e}")
            return False

    async def handle_admin_help(self, event):
        """معالجة أمر شرح الصلاحيات الإدارية"""
        if not self.is_admin(event.sender_id):
            return

        admin_help_text = """
🔐 **كيفية الحصول على صلاحيات إدارية للوصول لقائمة المشتركين:**

**📋 الخطوات المطلوبة:**

**1️⃣ إضافة البوت كمدير في القناة:**
• اذهب لإعدادات القناة
• اختر "المديرين" أو "Administrators"
• اضغط "إضافة مدير" أو "Add Admin"
• ابحث عن البوت: `@اسم_البوت`
• أضفه كمدير

**2️⃣ الصلاحيات المطلوبة للبوت:**
✅ **قراءة الرسائل** - لمراقبة التفاعلات
✅ **عرض قائمة المشتركين** - للوصول لقوائم المستخدمين
❌ **لا يحتاج صلاحيات أخرى** (إرسال رسائل، حذف، إلخ)

**3️⃣ بدائل إذا لم تستطع إضافة البوت كمدير:**

**أ) استخدام مجموعة بدلاً من قناة:**
• المجموعات أكثر انفتاحاً من القنوات
• يمكن للبوت الوصول لمعلومات أكثر
• أنشئ مجموعة عامة بدلاً من قناة

**ب) التركيز على المستخدمين النشطين:**
• البوت يمكنه تتبع من يرسل رسائل
• مراقبة المستخدمين النشطين فقط
• هذا لا يتطلب صلاحيات إدارية

**ج) استخدام طرق أخرى:**
• استطلاعات الرأي (يمكن رؤية المصوتين)
• طلب التعليقات (يمكن رؤية المعلقين)
• إنشاء بوت تفاعلي منفصل

**4️⃣ ما يعمل حالياً بدون صلاحيات:**
✅ مراقبة التفاعلات (العدد والنوع)
✅ إحصائيات التفاعلات
✅ تتبع المستخدمين النشطين (من الرسائل)
✅ تحليل نشاط القناة العام

**💡 الخلاصة:**
إذا كنت مدير القناة، أضف البوت كمدير بصلاحيات محدودة.
إذا لم تكن مدير، استخدم البدائل المتاحة أو اطلب من المدير إضافة البوت.
        """

        await event.respond(admin_help_text)

    async def handle_stop_monitoring(self, event):
        """معالجة أمر إيقاف المراقبة"""
        if not self.is_admin(event.sender_id):
            return
        
        if not self.is_monitoring:
            await event.respond("⚠️ المراقبة غير مفعلة حالياً.")
            return
        
        if self.scan_task:
            self.scan_task.cancel()
            self.scan_task = None
        
        if self.monitor_client:
            await self.monitor_client.disconnect()
            self.monitor_client = None
        
        self.is_monitoring = False
        self.monitoring_channel = None
        self.last_reactions = {}
        
        await event.respond("✅ تم إيقاف المراقبة الصادقة بنجاح.")

    async def handle_callback(self, event):
        """معالجة أزرار الاستجابة"""
        if not self.is_admin(event.sender_id):
            return

        data = event.data.decode('utf-8')

        if data == "stats":
            await self.handle_stats(event)
        elif data == "subscribers":
            await self.handle_subscribers(event)
        elif data == "admin_help":
            await self.handle_admin_help(event)
        elif data == "truth":
            await self.handle_truth(event)
        elif data == "alternatives":
            await self.handle_alternatives(event)

        await event.answer()

    def is_admin(self, user_id: int) -> bool:
        """التحقق من صلاحيات المدير"""
        return user_id == self.admin_user_id
    
    async def send_welcome_message(self):
        """إرسال رسالة ترحيب للمدير"""
        try:
            message = """
🤖 **تم تشغيل البوت الصادق بنجاح!**

✅ **هذا البوت صادق معك:**
• يخبرك الحقيقة حول قيود Telegram
• لا يدعي قدرات وهمية
• يركز على ما يمكن تحقيقه فعلياً

❌ **لن يكذب عليك:**
• لن يدعي معرفة أسماء المتفاعلين
• لن يعطي تخمينات عشوائية
• لن يخدعك بمعلومات مزيفة

استخدم /start للبدء.
            """
            await self.bot.send_message(self.admin_user_id, message)
        except Exception as e:
            print(f"لا يمكن إرسال رسالة الترحيب: {e}")
    
    async def run(self):
        """تشغيل البوت"""
        await self.start()
        await self.bot.run_until_disconnected()

async def main():
    print("🔄 بدء تشغيل البوت الصادق...")
    bot = HonestReactionBot()
    await bot.run()

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
