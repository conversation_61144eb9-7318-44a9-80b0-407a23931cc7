#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🤖 بوت مراقبة تفاعلات التليجرام
تشغيل سريع للبوت

الاستخدام:
python run.py
"""

import asyncio
import sys
import os
from pathlib import Path

# إضافة المجلد الحالي للمسار
sys.path.append(str(Path(__file__).parent))

from reaction_bot import ReactionBot

def check_environment():
    """التحقق من متغيرات البيئة المطلوبة"""
    required_vars = [
        'API_ID',
        'API_HASH', 
        'BOT_TOKEN',
        'PHONE_NUMBER',
        'ADMIN_USER_ID'
    ]
    
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print("❌ متغيرات البيئة المفقودة:")
        for var in missing_vars:
            print(f"   - {var}")
        print("\n💡 قم بإنشاء ملف .env من .env.example وملء البيانات المطلوبة")
        return False
    
    return True

def print_banner():
    """طباعة شعار البوت"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                                                              ║
║    🤖 بوت مراقبة تفاعلات التليجرام                          ║
║                                                              ║
║    📊 يراقب ويحلل تفاعلات قنوات التليجرام                   ║
║    💾 يحفظ البيانات في قاعدة بيانات محلية                   ║
║    📈 يوفر إحصائيات مفصلة وتقارير                          ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

async def main():
    """الدالة الرئيسية"""
    print_banner()
    
    # التحقق من متغيرات البيئة
    if not check_environment():
        return
    
    print("🔄 جاري تشغيل البوت...")
    
    try:
        # إنشاء وتشغيل البوت
        bot = ReactionBot()
        await bot.run()
        
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البوت بواسطة المستخدم")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
        print("\n💡 تأكد من:")
        print("   - صحة بيانات API")
        print("   - اتصال الإنترنت")
        print("   - صلاحيات البوت")

if __name__ == "__main__":
    # تشغيل البوت
    asyncio.run(main())
