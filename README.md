# 🤖 بوت مراقبة تفاعلات التليجرام

بوت قوي ومتقدم لمراقبة وتحليل تفاعلات قنوات التليجرام. يوفر إحصائيات مفصلة عن المتفاعلين والإيموجي الأكثر استخداماً.

## ✨ المميزات

- 🔍 **مراقبة فورية** للتفاعلات على الرسائل
- 👥 **تتبع المتفاعلين** مع حفظ أسمائهم ومعرفاتهم
- 📊 **إحصائيات مفصلة** لأكثر المتفاعلين والإيموجي الشائعة
- 💾 **قاعدة بيانات محلية** لحفظ جميع البيانات
- 🔔 **تنبيهات فورية** للمدير عند حدوث تفاعلات جديدة
- 📁 **تصدير البيانات** بصيغة JSON
- 🌐 **دعم اللغة العربية** بالكامل

## 🚀 التثبيت والإعداد

### 1. متطلبات النظام

```bash
Python 3.8+
pip (مدير حزم Python)
```

### 2. تثبيت المتطلبات

```bash
pip install -r requirements.txt
```

### 3. إعداد متغيرات البيئة

انسخ ملف `.env.example` إلى `.env`:

```bash
cp .env.example .env
```

املأ البيانات المطلوبة في ملف `.env`:

```env
# بيانات Telegram API
API_ID=your_api_id_here
API_HASH=your_api_hash_here
BOT_TOKEN=your_bot_token_here
PHONE_NUMBER=your_phone_number_here

# معرف المدير
ADMIN_USER_ID=your_user_id_here

# القناة المراد مراقبتها
TARGET_CHANNEL=@your_channel_username
```

### 4. الحصول على بيانات API

#### أ) API_ID و API_HASH:
1. اذهب إلى [my.telegram.org](https://my.telegram.org)
2. سجل دخولك برقم هاتفك
3. اذهب إلى "API Development Tools"
4. أنشئ تطبيق جديد واحصل على API_ID و API_HASH

#### ب) BOT_TOKEN:
1. ابحث عن [@BotFather](https://t.me/botfather) في التليجرام
2. أرسل `/newbot`
3. اتبع التعليمات لإنشاء بوت جديد
4. احصل على TOKEN

#### ج) ADMIN_USER_ID:
1. ابحث عن [@userinfobot](https://t.me/userinfobot)
2. أرسل له أي رسالة للحصول على معرفك

## 🎯 التشغيل

### التشغيل السريع:

```bash
python run.py
```

### التشغيل المتقدم:

```bash
python reaction_bot.py
```

## 📱 استخدام البوت

### الأوامر الأساسية:

- `/start` - بدء البوت وعرض القائمة الرئيسية
- `/help` - عرض المساعدة والتعليمات
- `/monitor @channel_name` - بدء مراقبة قناة معينة
- `/stop` - إيقاف المراقبة الحالية

### أوامر الإحصائيات:

- `/stats` - عرض الإحصائيات العامة
- `/top [عدد]` - أكثر المتفاعلين (افتراضي: 10)
- `/emojis [عدد]` - الإيموجي الأكثر استخداماً
- `/export` - تصدير البيانات إلى ملف JSON

### مثال على الاستخدام:

```
/monitor @my_channel
/top 20
/emojis 15
/export
```

## 🗂️ هيكل المشروع

```
telegram-reaction-monitor/
├── reaction_bot.py          # البوت الرئيسي
├── telegram_monitor.py      # مراقب التفاعلات
├── database.py             # إدارة قاعدة البيانات
├── run.py                  # ملف التشغيل السريع
├── requirements.txt        # المتطلبات
├── .env.example           # مثال على متغيرات البيئة
├── .env                   # متغيرات البيئة (يجب إنشاؤه)
└── README.md              # هذا الملف
```

## 📊 قاعدة البيانات

البوت يستخدم SQLite لحفظ البيانات في الجداول التالية:

- **messages**: معلومات الرسائل
- **reactions**: تفاصيل التفاعلات
- **daily_stats**: إحصائيات يومية

## 🔧 استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في API_ID/API_HASH**:
   - تأكد من صحة البيانات من my.telegram.org

2. **البوت لا يرى التفاعلات**:
   - تأكد من أن البوت عضو في القناة
   - تأكد من أن القناة تسمح بالتفاعلات

3. **خطأ في الصلاحيات**:
   - تأكد من أن ADMIN_USER_ID صحيح

## 🛡️ الأمان

- لا تشارك ملف `.env` مع أحد
- احتفظ بنسخة احتياطية من قاعدة البيانات
- استخدم البوت فقط مع القنوات التي تملكها أو لديك إذن لمراقبتها

## 📝 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام الشخصي والتعليمي.

## 🤝 المساهمة

نرحب بالمساهمات! يمكنك:
- الإبلاغ عن الأخطاء
- اقتراح مميزات جديدة
- تحسين الكود

## 📞 الدعم

إذا واجهت أي مشاكل، يمكنك:
1. مراجعة قسم استكشاف الأخطاء
2. التحقق من ملفات السجلات
3. التأكد من صحة الإعدادات

---

**ملاحظة**: هذا البوت مخصص للاستخدام التعليمي والشخصي. تأكد من احترام شروط استخدام التليجرام وخصوصية المستخدمين.
